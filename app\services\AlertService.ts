import * as FileSystem from "expo-file-system";
import { DatabaseService, AlertRecord, AlertFile } from './DatabaseService';
import { NetworkService, ApiResponse } from './NetworkService';

// 警報上傳結果介面
export interface AlertUploadResult {
  success: boolean;
  message?: string;
  shouldDelete?: boolean;
  shouldRetry?: boolean;
  retryDelay?: number;
  alertData?: any; // 包含格式化後的警報資料
  apiResponse?: any; // 包含 API 回應
}

// 警報服務類別
export class AlertService {
  private databaseService: DatabaseService;
  private networkService: NetworkService;
  private uploadInProgress: boolean = false;

  constructor(databaseService: DatabaseService, networkService: NetworkService) {
    this.databaseService = databaseService;
    this.networkService = networkService;
  }

  // 格式化日期時間
  private formatDateTime(date: Date | string): string {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    const pad = (n: number) => n.toString().padStart(2, '0');
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
  }

  // 檢查是否有待上傳的警報
  async hasUnuploadedAlerts(): Promise<boolean> {
    const alerts = await this.databaseService.getUnuploadedAlerts();
    return alerts.length > 0;
  }

  // 取得所有警報
  async getAllAlerts(): Promise<AlertRecord[]> {
    return await this.databaseService.getAllAlerts();
  }

  // 上傳單一警報
  async uploadSingleAlert(alert: AlertRecord): Promise<AlertUploadResult> {
    try {
      // 格式化警報資料
      const alertData = {
        ...alert,
        CheckTime: this.formatDateTime(alert.CheckTime),
        AlarmTime: this.formatDateTime(alert.AlarmTime),
      };

      // 上傳警報資料
      const response: ApiResponse = await this.networkService.uploadAlert(alertData);

      if (response.success !== true) {
        if (response.message === "delete") {
          // 伺服器要求刪除此警報
          await this.databaseService.deleteAlert(alert.id!);
          return {
            success: true,
            shouldDelete: true,
            message: '伺服器要求刪除警報',
            alertData,
            apiResponse: response
          };
        } else {
          // 上傳失敗，需要重試
          return {
            success: false,
            shouldRetry: true,
            retryDelay: 15000,
            message: response.message || '上傳失敗',
            alertData,
            apiResponse: response
          };
        }
      }

      // 上傳成功，更新狀態
      await this.databaseService.updateAlertUploadStatus(alert.id!, 1);

      // 如果有圖片，上傳圖片
      if (alert.AlarmType > 0) {
        const imageUploadResult = await this.uploadAlertImages(alert);
        if (!imageUploadResult.success && imageUploadResult.shouldRetry) {
          return imageUploadResult;
        }
      }

      // 清理已上傳的警報
      await this.databaseService.deleteAlert(alert.id!);
      await this.databaseService.deleteAlertFiles(alert.id!);

      return {
        success: true,
        message: '警報上傳成功',
        alertData,
        apiResponse: response
      };

    } catch (error) {
      console.error('Upload alert error:', error);
      return {
        success: false,
        shouldRetry: true,
        retryDelay: 15000,
        message: error instanceof Error ? error.message : '未知錯誤'
      };
    }
  }

  // 上傳警報圖片
  private async uploadAlertImages(alert: AlertRecord): Promise<AlertUploadResult> {
    try {
      const alertFiles = await this.databaseService.getAlertFiles(alert.id!);
      
      if (alertFiles.length === 0) {
        return { success: true };
      }

      // 檢查圖片是否過期（超過12小時）
      const checkTime = new Date(alert.CheckTime);
      const totalHours = Math.floor((new Date().getTime() - checkTime.getTime()) / (1000 * 60 * 60));
      
      if (totalHours >= 12) {
        // 圖片過期，直接刪除
        for (const file of alertFiles) {
          try {
            await FileSystem.deleteAsync(file.path);
          } catch (error) {
            console.warn('Failed to delete expired file:', file.path);
          }
        }
        return { success: true, message: '圖片已過期，已刪除' };
      }

      // 準備上傳資料
      const formData = new FormData();
      const mappedFiles = alertFiles.map(file => {
        formData.append('files', {
          uri: file.path,
          name: `${file.PhotoId}.jpg`,
          type: 'image/jpeg',
        } as any);

        return {
          AlertId: alert.Guid,
          CountryId: alert.CountryId,
          UserId: alert.UserId,
          PhotoId: file.PhotoId,
          TakeTime: file.time,
          Latitude: file.Latitude,
          Longitude: file.Longitude
        };
      });

      formData.append("datas", JSON.stringify(mappedFiles));

      // 上傳圖片
      await this.networkService.uploadAlertImages(formData);

      // 刪除本地圖片檔案
      for (const file of alertFiles) {
        try {
          await FileSystem.deleteAsync(file.path);
          console.log('Deleted file:', file.path);
        } catch (error) {
          console.warn('Failed to delete file:', file.path, error);
        }
      }

      return { success: true, message: '圖片上傳成功' };

    } catch (error) {
      console.error('Upload images error:', error);
      
      // 根據時間決定重試延遲
      const checkTime = new Date(alert.CheckTime);
      const totalHours = Math.floor((new Date().getTime() - checkTime.getTime()) / (1000 * 60 * 60));
      const retryDelay = 20000 * Math.max(totalHours, 1);

      return {
        success: false,
        shouldRetry: totalHours < 12,
        retryDelay,
        message: error instanceof Error ? error.message : '圖片上傳失敗'
      };
    }
  }

  // 批次上傳警報
  async uploadAlerts(): Promise<{
    processed: number;
    successful: number;
    failed: number;
    nextRetryDelay?: number;
  }> {
    if (this.uploadInProgress) {
      console.log('Upload already in progress');
      return { processed: 0, successful: 0, failed: 0 };
    }

    this.uploadInProgress = true;

    try {
      const unuploadedAlerts = await this.databaseService.getUnuploadedAlerts();
      
      if (unuploadedAlerts.length === 0) {
        return { processed: 0, successful: 0, failed: 0 };
      }

      // 只處理第一筆警報
      const alert = unuploadedAlerts[0];
      const result = await this.uploadSingleAlert(alert);

      if (result.success) {
        return { 
          processed: 1, 
          successful: 1, 
          failed: 0,
          nextRetryDelay: 1000 // 成功後1秒檢查下一筆
        };
      } else {
        return { 
          processed: 1, 
          successful: 0, 
          failed: 1,
          nextRetryDelay: result.retryDelay || 15000
        };
      }

    } finally {
      this.uploadInProgress = false;
    }
  }

  // 結束巡邏
  async endPatrol(prs: any, globalData: any): Promise<boolean> {
    try {
      // 取得2小時前到現在的巡邏記錄
      const startTime = new Date(prs.StartTime);
      startTime.setHours(startTime.getHours() - 2);
      const endTime = new Date(prs.StartTime);

      const sendprs = globalData.org.prs.filter((x: any) => {
        const xstartTime = new Date(x.StartTime);
        return !!x.CheckTime && x.Code === prs.Code && xstartTime >= startTime && xstartTime <= endTime;
      });

      if (sendprs.length === 0) {
        console.log('No patrol records to send');
        return true;
      }

      const sendData = sendprs.map((x: any) => ({
        PrecId: x.PrecId,
        CheckTime: x.CheckTime,
        username: globalData.userInfo.username,
        UserId: globalData.userInfo.UserId
      }));

      const response = await this.networkService.endPatrol(sendData);
      
      if (response.success !== true) {
        if (response.message === "delete") {
          console.log('Server requested to delete patrol records');
          return true;
        } else {
          throw new Error(response.message || 'End patrol failed');
        }
      }

      return true;

    } catch (error) {
      console.error('End patrol error:', error);
      throw error;
    }
  }

  // 清理過期資料
  async cleanupOldData(daysOld: number = 30): Promise<void> {
    await this.databaseService.cleanupOldAlerts(daysOld);
  }

  // 取得統計資訊
  async getStatistics() {
    return await this.databaseService.getStatistics();
  }
}
