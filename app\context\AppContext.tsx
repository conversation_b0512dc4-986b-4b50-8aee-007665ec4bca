import React, { createContext, useContext, useState, ReactNode, useEffect, useRef } from 'react';
import * as SQLite from 'expo-sqlite';
import { useDatabaseService } from '../hooks/useDatabaseService';
import { NetworkService, createNetworkService } from '../services/NetworkService';
import { AlertService } from '../services/AlertService';

// 定義應用程式上下文的介面，包含全域資料、狀態管理和功能函數
interface AppContextType {
  globalData: any;
  setGlobalData: (data: any) => void;
  uuidv4: () => string;
  formatDateTime: (date: Date) => string;
  UploadAlert: (caller:string) => void;
  endPatrol: (prs: any) => void;
  alertQueue: any[];
  setAlertQueue: (data: any[]) => void;
  logs: LogEntry[];
  setLogs: (data: LogEntry[]) => void;
  appMessage: string;
  setAppMessage: (message: string) => void;
  settings: any;
  setSettings: (data: any) => void;
  addLog: (msg: any) => void;
  db: SQLite.SQLiteDatabase | null;
  // 新增服務
  alertService: AlertService | null;
  networkService: NetworkService | null;
  isOnline: boolean;
}

// 定義日誌項目的介面結構
interface LogEntry {
  timestamp: number;
  message: string;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// 提供應用程式上下文的元件，管理全域狀態和功能
export function AppProvider({ children }: { children: ReactNode }) {
  // 使用資料庫服務 Hook
  const { databaseService, isLoading: dbLoading, error: dbError } = useDatabaseService();

  // 狀態管理
  const [db, setDb] = useState<SQLite.SQLiteDatabase | null>(null);
  const [isOnline, setIsOnline] = useState(false);
  const [alertQueue, setAlertQueue] = useState<any[]>([]);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [appMessage, setAppMessage] = useState('');
  const [uploadTrigger, setUploadTrigger] = useState(0);

  // 服務實例
  const [networkService, setNetworkService] = useState<NetworkService | null>(null);
  const [alertService, setAlertService] = useState<AlertService | null>(null);

  // Use useRef to persist uploadTimeoutId across renders
  const uploadTimeoutId = useRef<number | null>(null);
  const endPatrolTimeoutId = useRef<number | null>(null);
  const uploadInProcess = useRef<number | null>(0);

  // 應用程式設定，包含API網址和請求標頭
  const [settings, setSettings] = useState({
    apiUrl: "https://alertsystem.makalot.com.tw/AppPatrol",
    httpHeader: {
        "Content-Type": "application/json",
        "Authorization": "MAKALOTPATROL$&^%*&JF5a66asdlfjJDFl"
    }
  });

  const serverAddress = useRef(settings.apiUrl);

  // 初始化服務
  useEffect(() => {
    if (settings.apiUrl && settings.httpHeader) {
      const netService = createNetworkService({
        apiUrl: settings.apiUrl,
        httpHeader: settings.httpHeader,
        timeout: 10000,
        maxRetries: 2
      });
      setNetworkService(netService);
    }
  }, [settings.apiUrl, settings.httpHeader]);

  useEffect(() => {
    if (databaseService && networkService) {
      const alertSvc = new AlertService(databaseService, networkService);
      setAlertService(alertSvc);
    }
  }, [databaseService, networkService]);

  useEffect(() => {
    serverAddress.current = settings.apiUrl;
  }, [settings.apiUrl]);

  
  // 全域資料狀態，包含應用程式版本、使用者資訊、組織結構等
  const [globalData, setGlobalData] = useState<any>(
    { 
      AppVersion: '3.0',

      isLogin: false,
      isOnline: false,
      // _setting: {
      //   apiUrl: "http://**************:8020/AppPatrol",
      //   httpHeader: {
      //       "Content-Type": "application/json",
      //       "Authorization": "MAKALOTPATROL$&^%*&JF5a66asdlfjJDFl"
      //   }
      // },
      _words: {
        appName: "廠區巡邏系統",
        patrol: "巡邏作業",
        country: "產區",
        region: "廠區",
        factory: "工廠",
        point: "巡邏點",
        userid: "帳號",
        password: "密碼",
        changePass: "變更密碼",
        signin: "登入",
        signout: "登出",
        exitHint: "確定離開系統 ?",
        serverOut: "伺服器離線",
        newpwd: "新密碼",
        oldpwd: "原密碼",
        confpwd: "確認密碼",
        confirm: "確定",
        ok: "確定",
        delete: "刪除",
        cancel: "取消",
        required: "必須輸入",
        pwdMismatch: "新密碼與確認密碼不相同",
        potral: "巡邏作業",
        setPoint: "巡邏點RFID設定",
        setPatrol: "巡邏員RFID設定",
        arriveScan: "巡邏簽到",
        thisPoint: "巡邏點 :",
        checkTime: "巡邏時間:",
        nextPoint: "下個巡邏點:",
        leftPoint: "剩餘巡邏點數:",
        alert1: "火災",
        alert2: "水災",
        alert3: "人員入侵",
        alert4: "停電",
        alert5: "未關電",
        alert6: "其他",
        explain: "請說明",
        notInTime: "尚未進入巡邏期間",
        openCamera: "開啟相機",
        confirmAlertBtn: "確定送出",
        inputAlmsg: "請輸入事件內容",
        confirmAlert: "確定送出異常通報 ?",
        expired: "APP已經更新，請安裝最新版本",
        changeServer: "變更伺服器",
        success: "成功",
        failed: "失敗",
        missOrd: "巡邏順序錯誤",
        reScan: "請重新簽到"
      },
      version: '29',
      serial: '001',
      userInfo : {
        secret: { username: "", password: "" },
        isLogin: false,
        culture: "zh-TW",
        status: 0,
        role: "",
        Admin: false,
        UserName: ""
      },
      org: {
        Phone: "",
        Sms: "",
        countries: [
            { CountryId: "1", CountryName: "台灣" },
        ],
        regions: [
            { CountryId: "1", RegionId: "1", RegionName: "台灣北部" },
        ],
        factories: [
            { RegionId: "1", FactoryId: "1", FactoryName: "台灣北部工廠1" },
        ],
        points: [
            { FactoryId: "1", PointId: "1", PointName: "工廠1巡邏點1", CheckOrder: 1 },
        ],
        users: [],
        prs: [],
      },
    }
  );

  // 格式化日期時間為字串
  const formatDateTime = (date: Date): string => {
    // console.log('formatDateTime date', date);
    // console.log('typeof(date)', typeof(date));
    if (typeof(date) == 'string') {
        date = new Date(date);
    }
    const pad = (n: number) => n.toString().padStart(2, '0');
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
  };

  // 生成唯一識別碼
  const uuidv4 = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // 添加日誌記錄
  const addLog = (msg: any) => {
    setLogs((prevLogs: any[]) => [
      ...prevLogs,
      {
        timestamp: Date.now(),
        message: msg || ""
      }
    ]);
  }

  // 檢查網路連線狀態
  const checkNetworkStatus = async () => {
    // console.log('checkNetworkStatus...', serverAddress.current, settings.apiUrl);
    let responseOk = false;
    let currentApiUrl = serverAddress.current;
    // setSettings((prev: any) => {
    //   currentApiUrl = prev.apiUrl;
    //   return prev;
    // });
    try {
      const response = await fetch(currentApiUrl + "/connect", {
        method: 'POST',
        headers: settings.httpHeader,
        body: JSON.stringify({}),
      });

      responseOk = response.ok;

    } catch (error) {
        responseOk = false;
        // setAppMessage(currentApiUrl + "/connect" + JSON.stringify(error));
        setLogs((prevLogs: any[]) => [
          ...prevLogs,
          {
            timestamp: Date.now(),
            message: JSON.stringify(error)
          }
        ]);
        console.log('checkNetworkStatus...', false);
        setGlobalData((prev: any) => ({
            ...prev,
            isOnline: false
        }));
    }

    // setAppMessage('rOk.' + responseOk + new Date().toLocaleString());
    // console.log('net stat ...', responseOk);
    setIsOnline((prev: boolean) => {
        if (prev != responseOk) {
          setGlobalData((prev: any) => ({
            ...prev,
            isOnline: responseOk
          }));
          // setLogs((prevLogs: any[]) => [
          //   ...prevLogs,
          //   {
          //     timestamp: Date.now(),
          //     message: 'setGlobalData isOnline=' + responseOk
          //   }
          // ]);
        }
        return responseOk;
    });
  };

  useEffect(() => {
      const openDatabase = async () => {
        try {
          const database = await SQLite.openDatabaseAsync('patrol.db');
          // Optional: Initialize your tables here
          await database.execAsync(`
            PRAGMA journal_mode = WAL;
            CREATE TABLE IF NOT EXISTS alerts (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              Guid TEXT, PrecId TEXT, username TEXT,
              CountryId TEXT, FactoryId TEXT, PointId TEXT, UserId TEXT,
              CheckTime TEXT,
              AlarmType INTEGER, AlarmTime TEXT,
              Latitude REAL, Longitude REAL,
              AlarmDesc TEXT, Photo INTEGER,
              trys INTEGER, upload INTEGER
            );
            
            CREATE TABLE IF NOT EXISTS alertfiles (
              id INTEGER PRIMARY KEY,
              alertId INTEGER,
              path TEXT,
              PhotoId TEXT,
              time TEXT,
              Latitude REAL,
              Longitude REAL
            );
            
            CREATE TABLE IF NOT EXISTS userInfo (
              id INTEGER PRIMARY KEY,
              info TEXT,
              apiUrl TEXT,
              words TEXT
            );
          `);

          console.log('inside openDatabase db', database);
          await afterOpenDatabase(database);
          setDb(database);

          console.log('await checkNetworkStatus()', serverAddress.current, settings.apiUrl);
          await checkNetworkStatus();
          console.log('finish checkNetworkStatus()');

          setUploadTrigger(prev => prev + 1); // Trigger uploadAlert on initial load

        } catch (err) {
          console.error('Failed to open database or initialize tables:', err);
        }

      };
      openDatabase();
 
      const afterOpenDatabase = async (database: SQLite.SQLiteDatabase) => {
        if (!database) {
          return;
        }
        console.log('SELECT info,words,apiUrl FROM userInfo');
      //   database.getFirstAsync('SELECT info,words,apiUrl FROM userInfo').then((result: any) => {
      //     if (result == null) {
      //       if (!database) return;
      //       console.log('INSERT INTO userInfo');
      //       database.runAsync('INSERT INTO userInfo (info,words,apiUrl) VALUES (?,?,?)', 
      //         [JSON.stringify(globalData.userInfo), JSON.stringify(globalData._words), settings.apiUrl]);
      //     } else {
  
      //       setGlobalData((prev: any) => ({
      //         ...prev,
      //         userInfo: JSON.parse(result.info),
      //         _words: JSON.parse(result.words)
      //       }));

      //       serverAddress.current = result.apiUrl;
      //       console.log('userInfo.apiUrl', serverAddress.current);
      //       setSettings((prev: any) => ({
      //         ...prev,
      //         apiUrl: result.apiUrl
      //       }));
      //     }
      //   });      
      const result: any = await database.getFirstAsync('SELECT info,words,apiUrl FROM userInfo');
      if (result == null) {
        console.log('INSERT INTO userInfo');
        await database.runAsync(
          'INSERT INTO userInfo (info,words,apiUrl) VALUES (?,?,?)',
          [JSON.stringify(globalData.userInfo), JSON.stringify(globalData._words), settings.apiUrl]
        );
      } else {
        setGlobalData((prev: any) => ({
          ...prev,
          userInfo: JSON.parse(result.info),
          _words: JSON.parse(result.words)
        }));

        serverAddress.current = result.apiUrl;
        console.log('userInfo.apiUrl', serverAddress.current);
        setSettings((prev: any) => ({
          ...prev,
          apiUrl: result.apiUrl
        }));
      }
    }

    const intervalId = setInterval(checkNetworkStatus, 15000);

    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
      console.log('useEffect uploadTrigger >>> ' + uploadTrigger);
      if (uploadTrigger > 0) {
        uploadAlert_();
      }
  }, [uploadTrigger]);

  useEffect(() => {
    if (globalData.isLogin) {
      UploadAlert('login callback');
    }
  }, [globalData.isLogin]);


  // useEffect(() => {
  //   if (!!db) {
  //     console.log('AppProvider db is ready');
  //     UploadAlert('root');
  //   } else {
  //     console.log('AppProvider db is not ready yet');
  //   }
  // }, [db]);
    // 警報上傳嘗試函數
    // 包含重試機制，最多嘗試2次
    const tryUpload = async (data: any, apiName: string) => {
      const maxRetries = 2; // 最大重試次數
      let retryCount = 0; // 重試計數器
      while (retryCount < maxRetries) {
          try {
              // 設定10秒超時限制
              const timeoutPromise = new Promise((_, reject) => {
                  setTimeout(() => reject(new Error('請求超時')), 10000);
              });

              console.log('tryUpload', serverAddress.current);
              setLogs((prevLogs: LogEntry[]) => [
                  ...prevLogs,
                  {
                    timestamp: Date.now(),
                    message: '* tryUpload-' + serverAddress.current
                  }
                ]);

              const fetchPromise = fetch(serverAddress.current + "/" + apiName, {
                  method: 'POST',
                  headers: settings.httpHeader,
                  body: JSON.stringify(data),
              });


              const response1 = await Promise.race([fetchPromise, timeoutPromise]) as Response;
              // console.log('response1', response1);
              if (!response1.ok) {
                  console.log('!response1.ok');
                  setLogs((prevLogs: LogEntry[]) => [
                    ...prevLogs,
                    {
                      timestamp: Date.now(),
                      message: 'response1 - ' + JSON.stringify(response1)
                    }
                  ]);
                  throw new Error('upload failed');
              }
              return await response1.json();
          } catch (error) {
              retryCount++;
              console.log(`upload失敗，第 ${retryCount} 次重試`);
              if (retryCount >= maxRetries) {
                throw error;
              }
              setLogs((prevLogs: LogEntry[]) => [
                  ...prevLogs,
                  {
                    timestamp: Date.now(),
                    message: `*upload失敗，第 ${retryCount} 次重試`
                  }
                ]);
              await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒後重試
          }
      }
  };

      
  // 上傳警報資訊到伺服器
  /**
 * 上傳警報的入口函數
 * @param caller 呼叫來源標識，用於追蹤呼叫來源
 */
const UploadAlert = async (caller: string) => {
  if (uploadInProcess.current == 1) {
    console.log(' &&&&&&& uploadInProcess from ' + caller); // Access current value
    return;
  }
  uploadAlert_();
};


  /**
 * 實際執行警報上傳的核心函數
 * @param tid 時間標識，用於追蹤同一批次的上傳請求
 * 功能流程：
 * 1. 記錄日誌
 * 2. 從資料庫讀取待上傳警報
 * 3. 設定重試機制(maxRetries次)
 * 4. 格式化警報資料
 * 5. 嘗試上傳到API端點
 * 6. 根據API回應處理結果(成功/失敗/刪除)
 * 7. 更新資料庫狀態
 */
const uploadAlert_ = async () => {
    // 警報上傳主函數
    // tid: 時間標記，用於追蹤上傳流程
    console.log('((((uploadAlert_ ========== ' + new Date().toLocaleString() + ' ))))');
    setLogs((prevLogs: any[]) => [
      ...prevLogs,
      {
        timestamp: Date.now(),
        message: 'uploadAlert_'
      }
    ]);

    // console.log(`$$$$$ prs.length=${globalData.org.prs.length}`);
      
    if (!!uploadTimeoutId.current) { // Check current value
        // 清除上一個 setTimeout 計時器
        console.log(`      clearTimeout(${uploadTimeoutId.current})`); // Clear current value
        clearTimeout(uploadTimeoutId.current);
        uploadTimeoutId.current = null; // Reset to null
    }

    if (uploadInProcess.current !== 0) {
      console.log(' &&&&&&& uploadInProcess  ', uploadInProcess.current); // Access current value
      return;
    }

    // 檢查服務狀態
    if (!alertService) {
      console.log('Alert service not initialized');
      return;
    }

    // 從服務層查詢所有警報記錄
    const allAlerts = await alertService.getAllAlerts();

    console.log(`setAlertQueue 資料數 : ${allAlerts.length}`);
    setAlertQueue(allAlerts); // 更新警報陣列狀態
    if (allAlerts.length == 0) {
        console.log('uploadAlert_ alerts is null');
        // setTimeout(() => {
        //   setUploadTrigger(prev => prev + 1); // Trigger next upload
        // }, 35000); // 處理完畢，檢查是否有下一筆資料
        return;
    }
    
    // 取出第一筆警報資料
    const alerts = allAlerts[0];
    console.log('uploadAlert_ alerts', alerts);
    // console.log('typeof(alerts.CheckTime)', typeof(alerts.CheckTime));
    // console.log('typeof(alerts.AlarmTime)', typeof(alerts.AlarmTime));

    // 時間檢查已經在 AlertService 中處理

    try {
        if (alerts.upload == 0) {
            console.log('alerts.upload == 0');
            uploadInProcess.current = 1;

            // 使用新的服務層上傳單一警報
            const uploadResult = await alertService.uploadSingleAlert(alerts);
            console.log('uploadResult', uploadResult);

            uploadInProcess.current = 0;

            addLog(`retObj.success=${uploadResult.success}`);

            // 處理上傳結果
            if (uploadResult.success !== true) {
              if (uploadResult.shouldDelete) {
                // 伺服器要求刪除此警報記錄
                console.log('message == delete, 取消上傳');
                let prs = globalData.org.prs.find((e: any) => {
                    return e.PrecId == alerts.PrecId;
                });
                if (prs != null) {
                    prs.upload = false;
                    prs.CheckTime = null;
                    prs.msg = uploadResult.message;
                    console.log('取消上傳成功', prs);
                }
              } else {
                  // 上傳失敗，15秒後重試
                  console.warn('Upload success error 15秒重試');
                  uploadTimeoutId.current = setTimeout(() => { setUploadTrigger(prev => prev + 1) }, uploadResult.retryDelay || 15000);
                  return;
              }
            } else {
              // 上傳成功，服務層已經處理了資料庫更新
              console.log('Upload successful');
              // 處理簽到時間更新邏輯（只針對正常簽到）
              if (alerts.AlarmType == 0 || alerts.AlarmType == null) {
                addLog('*uploadResult.success');

                // 從 uploadResult 取得資料
                const alertData = uploadResult.alertData;
                const retObj = uploadResult.apiResponse;

                console.log('uploadResult.success', uploadResult.success);
                console.log('alertData.PrecId', alertData?.PrecId);
                console.log('retObj.PrecId', retObj?.PrecId);

                if (uploadResult.success && alertData && retObj && alertData.PrecId == retObj.PrecId) {
                    console.log('如果是重新傳送，必須寫入手機端的簽到時間', retObj.PrecId);

                    // 更新全域資料中的巡檢點簽到時間
                    let Prs = globalData.org.prs.find((e: any) => e.PrecId === retObj.PrecId);
                    console.log('全域資料巡檢點 globalData.org.prs', globalData.org.prs.length);
                    console.log('search result', Prs);
                    if (Prs != null) {
                        if (!Prs.CheckTime) Prs.CheckTime = new Date(alertData.CheckTime);
                        Prs.UserId = alertData.UserId;
                        Prs.upload = true;
                        globalData.org.prs.forEach((e: any) => {
                          if (e.Code == Prs.Code && e.StartTime == Prs.StartTime) {
                            if (e.UserId == null) e.UserId = alertData.UserId;
                          }
                        });

                        setGlobalData({
                          ...globalData,
                          org: {
                            ...globalData.org,
                            prs: globalData.org.prs,
                          },
                        });

                        addLog('寫入簽到時間成功');
                        console.log(`寫入簽到時間成功 `, Prs);
                    }
                }
              }
            }

        }

        // 圖片上傳和資料清理已經在 AlertService 中處理
        // console.log('result delete alerts', result, result2);
        // setLogs((prevLogs: LogEntry[]) => [
        //   ...prevLogs,
        //   {
        //     timestamp: Date.now(),
        //     message: 'uploadAlert_ 1秒後再上傳'
        //   }
        // ]);

        // 處理完畢，馬上檢查是否有下一筆資料
        uploadTimeoutId.current = setTimeout(() => { setUploadTrigger(prev => prev + 1) }, 1000);
        // console.log('uploadTimeoutId', uploadTimeoutId.current); // Access current value

    } catch (error) {
        // 全局錯誤處理，20秒後重試
        uploadInProcess.current = 0;
        console.warn('653 Upload alert error 15秒重試:');
        setLogs((prevLogs: LogEntry[]) => [
            ...prevLogs,
            {
              timestamp: Date.now(),
              message: '*Upload error 稍後重試-' + JSON.stringify(error)
            }
          ]);
        uploadTimeoutId.current = setTimeout(() => { setUploadTrigger(prev => prev + 1) }, 15000); // Assign to current
        console.log('uploadTimeoutId', uploadTimeoutId.current); // Access current value
      }
  };


  const endPatrol = async (prs: any) => {
    console.log('endPatrol prs', prs);

    if (!alertService) {
      console.log('Alert service not initialized');
      return;
    }

    if (!!endPatrolTimeoutId.current) {
        clearTimeout(endPatrolTimeoutId.current);
        endPatrolTimeoutId.current = null;
    }

    try {
      const success = await alertService.endPatrol(prs, globalData);

      if (success) {
        addLog('結束巡邏成功');
        console.log('End patrol successful');
      } else {
        throw new Error('End patrol failed');
      }

    } catch (error) {
      console.warn('endPatrol error 60秒重試:', error);
      addLog(`結束巡邏錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`);

      // 60秒後重試
      endPatrolTimeoutId.current = setTimeout(() => endPatrol(prs), 60000);
    }
  };

  return (
    <AppContext.Provider value={{
      globalData,
      setGlobalData,
      uuidv4,
      formatDateTime,
      UploadAlert,
      endPatrol,
      setAlertQueue,
      alertQueue,
      appMessage,
      setAppMessage,
      settings,
      setSettings,
      logs,
      setLogs,
      addLog,
      db,
      alertService,
      networkService,
      isOnline
      }}>
      {children}
    </AppContext.Provider>
  );
}

// 自定義Hook，用於在元件中使用應用程式上下文
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

export default AppProvider;