# 巡邏應用程式

這是一個使用 React Native 和 Expo 開發的巡邏應用程式。

## 已安裝套件

### Expo 核心套件
- `expo-router`: Expo 的路由導航系統
- `expo-sqlite`: SQLite 資料庫支援
- `expo-font`: 字體載入工具
- `expo-splash-screen`: 啟動畫面控制
- `expo-camera`: 相機功能支援
- `expo-location`: 地理位置服務

### React Navigation
- `@react-navigation/native`: React Navigation 核心套件

### UI 組件
- `@expo/vector-icons`: Expo 圖示庫
  - MaterialIcons
  - FontAwesome
- `@react-native-picker/picker`: 下拉選單組件

### 硬體整合
- `react-native-nfc-manager`: NFC 讀取與管理
- `expo-camera`: 相機控制與拍照功能

### 動畫
- `react-native-reanimated`: React Native 動畫庫

## 功能說明

### 硬體整合功能
- NFC 標籤讀取與寫入
- GPS 定位追蹤
- 相機拍照與影像處理
- 下拉選單多層級選擇（國家/區域/工廠/巡邏點）

## 資料庫結構

應用程式使用 SQLite 資料庫，包含以下表格：

### alerts 表格
用於儲存警報資訊

| 欄位名稱 | 資料類型 | 說明 |
|---------|---------|------|
| id | INTEGER PRIMARY KEY AUTOINCREMENT | 主鍵 |
| Guid | varchar(50) | 全域唯一識別碼 |
| PrecId | varchar(50) | 前置識別碼 |
| username | varchar(50) | 使用者名稱 |
| CountryId | varchar(50) | 國家代碼 |
| FactoryId | varchar(50) | 工廠代碼 |
| PointId | varchar(50) | 點位代碼 |
| UserId | varchar(50) | 使用者代碼 |
| CheckTime | datetime | 檢查時間 |
| AlarmType | INTEGER | 警報類型 |
| AlarmTime | datetime | 警報時間 |
| Latitude | REAL | 緯度 |
| Longitude | REAL | 經度 |
| AlarmDesc | varchar(255) | 警報描述 |
| Photo | INTEGER | 照片標記 |
| trys | INTEGER | 嘗試次數 |
| upload | INTEGER | 上傳標記 |

### alertfiles 表格
用於儲存警報相關檔案

| 欄位名稱 | 資料類型 | 說明 |
|---------|---------|------|
| id | INTEGER PRIMARY KEY | 主鍵 |
| alertId | INTEGER | 警報 ID |
| path | varchar(255) | 檔案路徑 |
| PhotoId | varchar(50) | 照片 ID |
| time | datetime | 時間戳記 |
| Latitude | REAL | 緯度 |
| Longitude | REAL | 經度 |

### userInfo 表格
用於儲存使用者資訊

| 欄位名稱 | 資料類型 | 說明 |
|---------|---------|------|
| id | INTEGER PRIMARY KEY | 主鍵 |
| info | varchar(500) | 使用者資訊 |
| apiUrl | varchar(100) | API 網址 |
| words | varchar(500) | 備註文字 |

## 主要功能
- 線上/離線狀態顯示
- 巡邏管理
- 警報系統
- 定位點設定
- 密碼變更
- NFC 標籤掃描
- GPS 定位記錄
- 照片拍攝與上傳

## 開發說明
此應用程式使用 Expo 開發框架，採用 TypeScript 作為開發語言。使用 SQLite 進行本地資料儲存，支援離線操作。

## 系統需求
- Node.js 14.0 或以上版本
- Expo CLI
- 支援 NFC 的行動裝置
- 支援 GPS 定位的行動裝置
- 註冊expo帳號

## 安裝步驟
1. 複製專案 
2. 註冊expo帳號
3. 安裝expo-cli
4. 安裝node.js
5. 安裝yarn
6. 安裝expo-router
7. 安裝expo-sqlite
8. 安裝react-native-nfc-manager
9. 安裝expo-camera
10. 安裝expo-location
11. 安裝@react-navigation/native
12. 安裝@expo/vector-icons
13. 安裝react-native-reanimated
14. 安裝@react-native-picker/picker
15. 安裝expo-splash-screen
16. 安裝expo-font

