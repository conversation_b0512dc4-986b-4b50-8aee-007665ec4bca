// 網路服務設定介面
export interface NetworkConfig {
  apiUrl: string;
  httpHeader: {
    'Content-Type': string;
    'Authorization': string;
  };
  timeout?: number;
  maxRetries?: number;
}

// API 回應介面
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  errmsg?: string;
  data?: T;
  [key: string]: any;
}

// 網路服務類別
export class NetworkService {
  private config: NetworkConfig;

  constructor(config: NetworkConfig) {
    this.config = {
      timeout: 10000,
      maxRetries: 2,
      ...config,
    };
  }

  // 更新設定
  updateConfig(newConfig: Partial<NetworkConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // 檢查網路連線狀態
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/connect', {});
      return response.ok;
    } catch (error) {
      console.log('Network connection check failed:', error);
      return false;
    }
  }

  // 上傳警報資料
  async uploadAlert(alertData: any): Promise<ApiResponse> {
    return await this.makeRequestWithRetry('/UploadAlert', alertData);
  }

  // 結束巡邏
  async endPatrol(patrolData: any[]): Promise<ApiResponse> {
    return await this.makeRequestWithRetry('/EndPatrol', patrolData);
  }

  // 上傳警報圖片
  async uploadAlertImages(formData: FormData): Promise<Response> {
    const response = await fetch(`${this.config.apiUrl}/UploadAlertImages`, {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (!response.ok) {
      throw new Error(`Upload images failed: ${response.status}`);
    }

    return response;
  }

  // 帶重試機制的請求
  private async makeRequestWithRetry(endpoint: string, data: any): Promise<ApiResponse> {
    let retryCount = 0;
    const maxRetries = this.config.maxRetries || 2;

    while (retryCount < maxRetries) {
      try {
        const response = await this.makeRequest(endpoint, data);
        
        if (!response.ok) {
          throw new Error(`Request failed: ${response.status}`);
        }

        return await response.json();
      } catch (error) {
        retryCount++;
        console.log(`Request failed, retry ${retryCount}/${maxRetries}:`, error);
        
        if (retryCount >= maxRetries) {
          throw error;
        }
        
        // 等待 3 秒後重試
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    throw new Error('Max retries exceeded');
  }

  // 基本請求方法
  private async makeRequest(endpoint: string, data: any): Promise<Response> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), this.config.timeout);
    });

    const fetchPromise = fetch(`${this.config.apiUrl}${endpoint}`, {
      method: 'POST',
      headers: this.config.httpHeader,
      body: JSON.stringify(data),
    });

    return await Promise.race([fetchPromise, timeoutPromise]);
  }
}

// 網路服務的工廠函數
export const createNetworkService = (config: NetworkConfig): NetworkService => {
  return new NetworkService(config);
};
