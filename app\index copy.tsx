import { StatusBar } from 'expo-status-bar';
import { StyleSheet, TouchableOpacity, Image} from 'react-native';
import { type NavigationProp } from '@react-navigation/native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useAppContext } from './context/AppContext';
import { useEffect, useState } from 'react';
import Constants from 'expo-constants';
import LoginScreen from './login';
import SelectFactory from './selectFactory';
import { Text, View } from '@/components/Themed';
import { router } from 'expo-router';

export default function HomeScreen() {
    const [isLogin, setIsLogin] = useState(false);
    const [isFactoryModalVisible, setIsFactoryModalVisible] = useState(false);
    const { globalData, setGlobalData, alertQueue, logs, setLogs } = useAppContext();
    const [clicks, setClicks] = useState<number[]>([]);
    const [isDebug, setIsDebug] = useState(false);

    const handleVersionClick = () => {
        const now = Date.now();
        setClicks(prev => [...prev, now]);
        
        const recentClicks = [...clicks, now].slice(-5);
        
        if (recentClicks.length === 5) {
            const timeDiff = recentClicks[4] - recentClicks[0];
            if (timeDiff <= 2000) {
              setIsDebug(!isDebug);
              // router.push('./changeUrl');
              setClicks([]);
            }

        }
    };

    // useEffect(() => {
    //     navigation.setOptions({
    //       headerRight: () => (
    //         <MaterialIcons 
    //           name={globalData.isOnline ? "wifi" : "wifi-off"} 
    //           size={24} 
    //           color={globalData.isOnline ? "#4CAF50" : "#F44336"}
    //           style={{ marginRight: 16 }}
    //         />
    //       ),
    //     });
    //   }, [navigation, globalData.isOnline]);

    const navAlarm = () => {
      if (globalData.org.factories.length > 1) {
        setIsFactoryModalVisible(true);
      } else {
        router.push('./alarm');
      }
    }

    const statusBarHeight = Constants.statusBarHeight;

    return (
        <View style={[styles.container, {paddingTop: statusBarHeight}]}>
          <StatusBar style="light" backgroundColor="#666" />
          <View style={styles.header}>


            <Image source={require('../assets/images/namelogo.png')} style={{width: 240, height: 120}} resizeMode="stretch" />
            <Text style={{marginTop: 20,fontSize: 20, fontWeight: 'bold'}}>{globalData._words.appName}</Text>
          </View>
        {isLogin && (
          <View style={[styles.buttonContainer, {backgroundColor: 'transparent'}]}>            

            <Text style={{}}>{globalData.userInfo.username}</Text>

            {!globalData.userInfo.Admin && (
              <TouchableOpacity
                style={styles.button}
                onPress={() => router.push('./patrol')}
            >
                <Text style={styles.buttonText}>{globalData._words.patrol}</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={styles.button}
              onPress={navAlarm}
            >
              <Text style={styles.buttonText}>{globalData._words.alert}</Text>
            </TouchableOpacity>
            
            {globalData.userInfo.Admin && (
              <TouchableOpacity
                style={styles.button}
                onPress={() => router.push('./points')}
              >
                <Text style={styles.buttonText}>{globalData._words.setPoint}</Text>
              </TouchableOpacity>
            )}
            
            {!globalData.userInfo.Admin && (
              <TouchableOpacity
                style={styles.button}
                onPress={() => router.push('./changepass')}
              >
                <Text style={styles.buttonText}>{globalData._words.changePass}</Text>
              </TouchableOpacity>
            )}
            
            {isDebug && (
              <TouchableOpacity
                style={styles.button}
                onPress={() => router.push('./logs')}
              >
                <Text style={styles.buttonText}>系統紀錄</Text>
              </TouchableOpacity>
            )}
            
            {isDebug && (
              <TouchableOpacity
                style={styles.button}
                onPress={() => router.push('./logs')}
              >
                <Text style={styles.buttonText}>變更伺服器網址</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.button, styles.logoutButton]}
              onPress={() => setIsLogin(false)}
            >
              <Text style={styles.buttonText}>{globalData._words.signout}</Text>
            </TouchableOpacity>
          </View>
        )}
        {!isLogin && <LoginScreen setIsLogin={setIsLogin} />}

        <View style={{backgroundColor: 'transparent', alignItems: 'center'}}>
            <Text style={{fontSize: 16}}>{isDebug ? 'Queue:' + alertQueue.length : ''}</Text>
        </View>

        <View style={{backgroundColor: 'transparent', alignItems: 'center', paddingBottom: 16}}>
          <TouchableOpacity onPress={handleVersionClick}>
            <Text style={{fontSize: 16, marginTop: 10}}>{globalData.AppVersion}</Text>
          </TouchableOpacity>
        </View>
       



        <SelectFactory
          isVisible={isFactoryModalVisible}
          onClose={() => setIsFactoryModalVisible(false)}
          onSelect={(factoryId) => {
            let factoryName = globalData.org.factories.find((factory: any) => factory.FactoryId === factoryId)?.FactoryName;
            setGlobalData((prev: any) => ({
              ...prev,
              userInfo: {
                ...prev.userInfo,
                FactoryId: factoryId,
                FactoryName: factoryName
              }
            }));

            setIsFactoryModalVisible(false);
            router.push('./alarm');
          }}
        />
      </View>
    );
//   return (
//     <View style={styles.container}>
//       <Text style={styles.title}>Home</Text>
//       <View style={styles.separator} lightColor="#eee" darkColor="rgba(255,255,255,0.1)" />

//       <TouchableOpacity style={styles.button}
//         onPress={() => router.push('./two')}
//       >
//         <Text style={styles.buttonText}>Go to Details</Text>
//       </TouchableOpacity>


//     </View>
//   );
}

const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#afd4f3',
    },
    header: {
      backgroundColor: 'transparent',
      paddingTop: 10,
      paddingBottom: 10,
      alignItems: 'center',
    },
    buttonContainer: {
      justifyContent: 'space-around',
      alignItems: 'center',
      width: '100%',
      padding: 10,
      flex: 1,
    },


    button: {
      textAlign: 'center',
      // marginTop: 30,
      padding: 10,
      backgroundColor: '#cdeb87',
      alignItems: 'center',
      minWidth: '72%',
      borderRadius: 8,
    },
    buttonText: {
      color: '#333',
      textAlign: 'center',
    },
    logoutButton: {
      backgroundColor: 'gold',
      alignItems: 'center',
    },
    networkStatus: {
      position: 'absolute',
      top: 20,
      right: 20,
    },
  });

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   title: {
//     fontSize: 20,
//     fontWeight: 'bold',
//   },
//   separator: {
//     marginVertical: 30,
//     height: 1,
//     width: '80%',
//   },
//   button: {
//     backgroundColor: 'blue',
//     padding: 10,
//     borderRadius: 5,
//   },
//   buttonText: {
//     color: 'white',
//     fontSize: 16,
//     fontWeight: 'bold',
//   },
// });
