import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity } from 'react-native';
// import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useAppContext } from './context/AppContext';
import { Picker } from '@react-native-picker/picker';
import NfcManager, { NfcTech, NfcEvents } from 'react-native-nfc-manager';
import * as Location from 'expo-location';

const PointScreen = () => {
  console.log('render PointScreen...');
  const { globalData } = useAppContext();
  const [locationSwitch, setLocationSwitch] = useState(false);
  const locationSwitchRef = useRef(locationSwitch);

  const [address, setAddress] = useState({ 
    CountryId: '', RegionId: '', FactoryId: '', PointId: '', TagId: '', 
    DeviceId: '', Latitude: 0, Longitude: 0
  });

  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedFactory, setSelectedFactory] = useState('');
//   const [selectedPoint, setSelectedPoint] = useState('');

//   console.log("render points", address.TagId, address.PointId);
//   console.log("render points", !address.TagId, !address.PointId);
//   console.log("render points", !address.TagId || !address.PointId);

  // 根據選擇的國家過濾區域
  const filteredRegions = globalData.org.regions.filter(
    (region: { CountryId: string }) => region.CountryId === selectedCountry
  );

  // 根據選擇的區域過濾工廠
  const filteredFactories = globalData.org.factories.filter(
    (factory: { RegionId: string }) => factory.RegionId === selectedRegion
  );

  // 根據選擇的工廠過濾點位
  const filteredPoints = globalData.org.points.filter(
    (point: { FactoryId: string }) => point.FactoryId === selectedFactory
  );

//   useEffect(() => {
//     const initNfc = async () => {
//       try {
//         // 初始化 NFC
//         await NfcManager.start();
        
//         // 註冊事件監聽器
//         NfcManager.setEventListener(NfcEvents.DiscoverTag, async (tag: any) => {
//         //   alert('setEventListener:' + JSON.stringify(tag));
//           if (!!tag && !!tag.id) {
//             setAddress({...address, TagId: tag.id});
//             return;
//           }
//           try {
//             const tagData = await NfcManager.getTag();
//             alert('getTag:' + JSON.stringify(tagData));
//             if (!!tagData && !!tagData.id) {
//               setAddress({...address, TagId: tagData.id});
//               alert('setAddress:' + tagData.id);
//             }
//           } catch (error) {
//             console.error('讀取標籤資料時發生錯誤:', error);
//           }
//         });

//         // 開始監聽 NFC 標籤
//         await NfcManager.registerTagEvent();
        
//       } catch (ex) {
//         console.error('NFC初始化錯誤:', ex);
//         alert('NFC初始化失敗，請確認設備是否支援NFC並已開啟');
//       }
//     };

//     initNfc();

//     // 清理函數
//     return () => {
//       const cleanUp = async () => {
//         try {
//           NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
//           await NfcManager.unregisterTagEvent();
//           await NfcManager.cancelTechnologyRequest();
//         } catch (error) {
//           console.error('清理NFC時發生錯誤:', error);
//         }
//       };
//       cleanUp();
//     };
//   }, []);
useEffect(() => {
    const initNfc = async () => {
      try {
        await NfcManager.start();
        
        // 設置監聽器來持續檢測NFC標籤
        NfcManager.setEventListener(NfcEvents.DiscoverTag, async (tag: any) => {
          alert('setEventListener:' + JSON.stringify(tag));
          let tagResult = '';
          if (!!tag && !!tag.id) {
            setAddress({...address, TagId: tag.id});
            tagResult = tag.id;
          } else {
            const tagData = await NfcManager.getTag();
            alert('tagData:' + JSON.stringify(tagData));
            if (tagData && tagData.id) {
              setAddress({...address, TagId: tagResult});
              tagResult = tagData.id;
            }    
          }
          alert('tagResult' + tagResult);
          alert('locationSwitch' + locationSwitch);
          if (!!tagResult && locationSwitchRef.current == true) {
            const { status } = await Location.requestForegroundPermissionsAsync();
            alert('取得位置******* ' + status);
            if (status === 'granted') {
              let scanLocation = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Low,
                timeInterval: 5000,  // 5秒的時間間隔
                distanceInterval: 10 // 10公尺的距離間隔
              });
              setAddress({...address, TagId: tagResult, Latitude: scanLocation.coords.latitude, Longitude: scanLocation.coords.longitude});
              alert('scanLocation' + JSON.stringify(scanLocation));
            } else {
              setAddress({...address, TagId: tagResult});
            }
          } else if (!!tagResult) {
            setAddress({...address, TagId: tagResult});
          }
      });

        // 開始監聽 NFC 標籤
        await NfcManager.registerTagEvent();
      } catch (ex) {
        console.warn('NFC初始化錯誤:', ex);
        setAddress({...address, TagId: 'NFC not enabled'});
      }
    };

    initNfc();
    setAddress({...address, TagId: 'scanning...'});

    // 清理函數
    return () => {
      const cleanUp = async () => {
        try {
          NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
          await NfcManager.unregisterTagEvent();
          // await NfcManager.cancelTechnologyRequest();
        } catch (error) {
          console.log('清理NFC時發生錯誤:', error);
        }
      };
      cleanUp();
    };
  }, []);


  // 當國家改變時，重設區域和工廠
  useEffect(() => {
    setSelectedRegion('');
    setSelectedFactory('');
    setAddress({...address, PointId: ""});
}, [selectedCountry]);

  // 當區域改變時，重設工廠
  useEffect(() => {
    setSelectedFactory('');
    setAddress({...address, PointId: ""});
}, [selectedRegion]);

  // 當工廠改變時，重設點位
  useEffect(() => {
    setAddress({...address, PointId: ""});
  }, [selectedFactory]);

  return (
    <View style={styles.container}>
      <View style={styles.pickerContainer}>
        <Text style={styles.label}>國家</Text>
        <Picker
          style={styles.picker}
          selectedValue={selectedCountry || "_empty_"}
          onValueChange={(itemValue: string) => setSelectedCountry(itemValue === "_empty_" ? "" : itemValue)}>
          <Picker.Item label="請選擇國家" value="_empty_" style={styles.pickerItem} />
          {globalData.org.countries
            .sort((a: { CountryName: string }, b: { CountryName: string }) => a.CountryName.localeCompare(b.CountryName))
            .map((country: { CountryId: string; CountryName: string }) => (
              <Picker.Item 
                key={country.CountryId} 
                label={country.CountryName} 
                value={country.CountryId} 
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>區域</Text>
        <Picker
          style={styles.picker}
          selectedValue={selectedRegion || "_empty_"}
          enabled={!!selectedCountry}
          onValueChange={(itemValue: string) => setSelectedRegion(itemValue === "_empty_" ? "" : itemValue)}>
          <Picker.Item label="請選擇區域" value="_empty_" style={styles.pickerItem} />
          {filteredRegions
            .sort((a: { RegionName: string }, b: { RegionName: string }) => a.RegionName.localeCompare(b.RegionName))
            .map((region: { RegionId: string; RegionName: string }) => (
              <Picker.Item 
                key={region.RegionId} 
                label={region.RegionName} 
                value={region.RegionId} 
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>工廠</Text>
        <Picker
          style={styles.picker}
          selectedValue={selectedFactory || "_empty_"}
          enabled={!!selectedRegion}
          onValueChange={(itemValue: string) => setSelectedFactory(itemValue === "_empty_" ? "" : itemValue)}>
          <Picker.Item label="請選擇工廠" value="_empty_" style={styles.pickerItem} />
          {filteredFactories
            .sort((a: { FactoryName: string }, b: { FactoryName: string }) => a.FactoryName.localeCompare(b.FactoryName))
            .map((factory: { FactoryId: string; FactoryName: string }) => (
              <Picker.Item 
                key={factory.FactoryId} 
                label={factory.FactoryName}
                value={factory.FactoryId}
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>巡邏點</Text>
        <Picker
          style={styles.picker}
          selectedValue={address.PointId || "_empty_"}
          enabled={!!selectedFactory}
          onValueChange={(itemValue: string) => {
            // setSelectedPoint(itemValue === "_empty_" ? "" : itemValue);
            setAddress(prev => ({...prev, PointId: itemValue === "_empty_" ? "" : itemValue}));
          }}>
          <Picker.Item label="請選擇巡邏點" value="_empty_" style={styles.pickerItem} />
          {filteredPoints
            .sort((a: { CheckOrder: number }, b: { CheckOrder: number }) => a.CheckOrder - b.CheckOrder)
            .map((point: { PointId: string; PointName: string }) => (
              <Picker.Item 
                key={point.PointId} 
                label={point.PointName} 
                value={point.PointId}
                style={styles.pickerItem}
              />
            ))}
        </Picker>
        <View style={{marginTop: 20, flexDirection: 'row', justifyContent: 'flex-start'}}>
          <Text>標籤 ID : </Text>
          <Text style={{color: 'red'}}>{address.TagId}</Text>
        </View>
        <View style={styles.switchContainer}>
            <Text>GPS LOCATION：</Text>
            <Switch
              value={locationSwitch}
              onValueChange={(value) => {locationSwitchRef.current = value; setLocationSwitch(value);}}
            />
          </View>
        <View style={{marginTop: 20, alignItems: 'center'}}>
          <TouchableOpacity 
              style={[
                styles.touchButton,
                styles.sendButton,
                (!address.TagId || !address.PointId) && styles.sendButtonDisabled
              ]} 
              disabled={!address.TagId || !address.PointId}
              onPress={() => { 
                setAddress({...address, TagId: ""}); 
                setAddress({...address, TagId: "test"}); 
              }}
            >
              <Text style={styles.buttonText}>{globalData._words.confirmAlertBtn}</Text>
            </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 11,
  },
  pickerContainer: {
    width: '100%',
  },
  label: {
    fontSize: 14,
    marginBottom: 4,
    color: '#333',
  },
  picker: {
    backgroundColor: '#a5f5f5',
    marginBottom: 4,
    borderRadius: 4,
  },
  pickerItem: {
    fontSize: 14,
    height: 38,
    padding: 0,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  touchButton: {
    width: '60%',
    padding: 10,
    paddingTop: 4,
    borderRadius: 5,
    marginTop: 10,
    alignItems: 'center',
  },
  sendButton: {
    backgroundColor: '#4CAF50',
  },
  sendButtonDisabled: {
    backgroundColor: '#A5D6A7',  // 使用較淺的綠色
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
  },
});

export default PointScreen;
