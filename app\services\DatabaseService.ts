import * as SQLite from 'expo-sqlite';

// 定義資料庫實體介面
export interface AlertRecord {
  id?: number;
  Guid: string;
  PrecId: string;
  username: string;
  CountryId: string;
  FactoryId: string;
  PointId: string;
  UserId: string;
  CheckTime: string;
  AlarmType: number;
  AlarmTime: string;
  Latitude: number;
  Longitude: number;
  AlarmDesc: string;
  Photo: number;
  trys: number;
  upload: number;
}

export interface AlertFile {
  id?: number;
  alertId: number;
  path: string;
  PhotoId: string;
  time: string;
  Latitude: number;
  Longitude: number;
}

export interface UserInfo {
  id?: number;
  info: string;
  apiUrl: string;
  words: string;
}

// 資料存取層服務類別
export class DatabaseService {
  private db: SQLite.SQLiteDatabase;

  constructor(database: SQLite.SQLiteDatabase) {
    this.db = database;
  }

  // 初始化資料庫表格
  async initializeTables(): Promise<void> {
    await this.db.execAsync(`
      PRAGMA journal_mode = WAL;
      CREATE TABLE IF NOT EXISTS alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        Guid TEXT, PrecId TEXT, username TEXT,
        CountryId TEXT, FactoryId TEXT, PointId TEXT, UserId TEXT,
        CheckTime TEXT,
        AlarmType INTEGER, AlarmTime TEXT,
        Latitude REAL, Longitude REAL,
        AlarmDesc TEXT, Photo INTEGER,
        trys INTEGER, upload INTEGER
      );
      
      CREATE TABLE IF NOT EXISTS alertfiles (
        id INTEGER PRIMARY KEY,
        alertId INTEGER,
        path TEXT,
        PhotoId TEXT,
        time TEXT,
        Latitude REAL,
        Longitude REAL
      );
      
      CREATE TABLE IF NOT EXISTS userInfo (
        id INTEGER PRIMARY KEY,
        info TEXT,
        apiUrl TEXT,
        words TEXT
      );
    `);
  }

  // Alert 相關操作
  async getAllAlerts(): Promise<AlertRecord[]> {
    return await this.db.getAllAsync<AlertRecord>('SELECT * FROM alerts ORDER BY id');
  }

  async getUnuploadedAlerts(): Promise<AlertRecord[]> {
    return await this.db.getAllAsync<AlertRecord>(
      'SELECT * FROM alerts WHERE upload = 0 ORDER BY id'
    );
  }

  async insertAlert(alert: Omit<AlertRecord, 'id'>): Promise<SQLite.SQLiteRunResult> {
    const {
      Guid, PrecId, username, CountryId, FactoryId, PointId, UserId,
      CheckTime, AlarmType, AlarmTime, Latitude, Longitude, AlarmDesc,
      Photo, trys, upload
    } = alert;

    return await this.db.runAsync(
      `INSERT INTO alerts (
        Guid, PrecId, username, CountryId, FactoryId, PointId, UserId,
        CheckTime, AlarmType, AlarmTime, Latitude, Longitude, AlarmDesc,
        Photo, trys, upload
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        Guid, PrecId, username, CountryId, FactoryId, PointId, UserId,
        CheckTime, AlarmType, AlarmTime, Latitude, Longitude, AlarmDesc,
        Photo, trys, upload
      ]
    );
  }

  async updateAlertUploadStatus(id: number, uploadStatus: number): Promise<SQLite.SQLiteRunResult> {
    return await this.db.runAsync('UPDATE alerts SET upload = ? WHERE id = ?', [uploadStatus, id]);
  }

  async deleteAlert(id: number): Promise<SQLite.SQLiteRunResult> {
    return await this.db.runAsync('DELETE FROM alerts WHERE id = ?', [id]);
  }

  // AlertFile 相關操作
  async getAlertFiles(alertId: number): Promise<AlertFile[]> {
    return await this.db.getAllAsync<AlertFile>(
      'SELECT * FROM alertfiles WHERE alertId = ?',
      [alertId]
    );
  }

  async insertAlertFile(alertFile: Omit<AlertFile, 'id'>): Promise<SQLite.SQLiteRunResult> {
    const { alertId, path, PhotoId, time, Latitude, Longitude } = alertFile;
    return await this.db.runAsync(
      'INSERT INTO alertfiles (alertId, path, PhotoId, time, Latitude, Longitude) VALUES (?, ?, ?, ?, ?, ?)',
      [alertId, path, PhotoId, time, Latitude, Longitude]
    );
  }

  async deleteAlertFiles(alertId: number): Promise<SQLite.SQLiteRunResult> {
    return await this.db.runAsync('DELETE FROM alertfiles WHERE alertId = ?', [alertId]);
  }

  // UserInfo 相關操作
  async getUserInfo(): Promise<UserInfo | null> {
    return await this.db.getFirstAsync<UserInfo>('SELECT info, words, apiUrl FROM userInfo');
  }

  async insertUserInfo(userInfo: Omit<UserInfo, 'id'>): Promise<SQLite.SQLiteRunResult> {
    const { info, apiUrl, words } = userInfo;
    return await this.db.runAsync(
      'INSERT INTO userInfo (info, words, apiUrl) VALUES (?, ?, ?)',
      [info, words, apiUrl]
    );
  }

  async updateUserInfo(userInfo: Omit<UserInfo, 'id'>): Promise<SQLite.SQLiteRunResult> {
    const { info, apiUrl, words } = userInfo;
    return await this.db.runAsync(
      'UPDATE userInfo SET info = ?, words = ?, apiUrl = ?',
      [info, words, apiUrl]
    );
  }

  // 交易操作
  async executeTransaction<T>(operations: (db: SQLite.SQLiteDatabase) => Promise<T>): Promise<T> {
    return await this.db.withTransactionAsync(async () => {
      return await operations(this.db);
    });
  }

  // 清理過期資料
  async cleanupOldAlerts(daysOld: number = 30): Promise<SQLite.SQLiteRunResult> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    const cutoffTimestamp = cutoffDate.toISOString();

    return await this.db.runAsync(
      'DELETE FROM alerts WHERE upload = 1 AND CheckTime < ?',
      [cutoffTimestamp]
    );
  }

  // 取得資料庫統計資訊
  async getStatistics(): Promise<{
    totalAlerts: number;
    unuploadedAlerts: number;
    totalAlertFiles: number;
  }> {
    const totalAlertsResult = await this.db.getFirstAsync<{ count: number }>(
      'SELECT COUNT(*) as count FROM alerts'
    );
    const unuploadedAlertsResult = await this.db.getFirstAsync<{ count: number }>(
      'SELECT COUNT(*) as count FROM alerts WHERE upload = 0'
    );
    const totalAlertFilesResult = await this.db.getFirstAsync<{ count: number }>(
      'SELECT COUNT(*) as count FROM alertfiles'
    );

    return {
      totalAlerts: totalAlertsResult?.count || 0,
      unuploadedAlerts: unuploadedAlertsResult?.count || 0,
      totalAlertFiles: totalAlertFilesResult?.count || 0,
    };
  }
}

// 資料庫服務的工廠函數
export const createDatabaseService = async (databaseName: string = 'patrol.db'): Promise<DatabaseService> => {
  const database = await SQLite.openDatabaseAsync(databaseName);
  const service = new DatabaseService(database);
  await service.initializeTables();
  return service;
};
