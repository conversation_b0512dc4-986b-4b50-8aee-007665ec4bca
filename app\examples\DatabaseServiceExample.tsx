import React, { useEffect, useState } from 'react';
import { View, Text, Button, ScrollView, StyleSheet } from 'react-native';
import { useAppContext } from '../context/AppContext';
import { AlertRecord } from '../services/DatabaseService';

// 使用資料存取層的範例元件
export const DatabaseServiceExample: React.FC = () => {
  const { alertService, networkService, isOnline, addLog } = useAppContext();
  const [alerts, setAlerts] = useState<AlertRecord[]>([]);
  const [statistics, setStatistics] = useState<{
    totalAlerts: number;
    unuploadedAlerts: number;
    totalAlertFiles: number;
  } | null>(null);

  // 載入警報資料
  const loadAlerts = async () => {
    if (!alertService) return;
    
    try {
      const allAlerts = await alertService.getAllAlerts();
      setAlerts(allAlerts);
      addLog(`載入 ${allAlerts.length} 筆警報記錄`);
    } catch (error) {
      console.error('Failed to load alerts:', error);
      addLog(`載入警報失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
    }
  };

  // 載入統計資訊
  const loadStatistics = async () => {
    if (!alertService) return;
    
    try {
      const stats = await alertService.getStatistics();
      setStatistics(stats);
      addLog(`統計資訊: 總計 ${stats.totalAlerts} 筆警報，${stats.unuploadedAlerts} 筆待上傳`);
    } catch (error) {
      console.error('Failed to load statistics:', error);
      addLog(`載入統計失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
    }
  };

  // 檢查網路連線
  const checkConnection = async () => {
    if (!networkService) return;
    
    try {
      const connected = await networkService.checkConnection();
      addLog(`網路連線狀態: ${connected ? '已連線' : '離線'}`);
    } catch (error) {
      console.error('Failed to check connection:', error);
      addLog(`檢查網路失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
    }
  };

  // 上傳警報
  const uploadAlerts = async () => {
    if (!alertService) return;
    
    try {
      const result = await alertService.uploadAlerts();
      addLog(`上傳結果: 處理 ${result.processed} 筆，成功 ${result.successful} 筆，失敗 ${result.failed} 筆`);
      
      // 重新載入資料
      await loadAlerts();
      await loadStatistics();
    } catch (error) {
      console.error('Failed to upload alerts:', error);
      addLog(`上傳警報失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
    }
  };

  // 清理過期資料
  const cleanupOldData = async () => {
    if (!alertService) return;
    
    try {
      await alertService.cleanupOldData(30); // 清理30天前的資料
      addLog('清理過期資料完成');
      
      // 重新載入資料
      await loadAlerts();
      await loadStatistics();
    } catch (error) {
      console.error('Failed to cleanup old data:', error);
      addLog(`清理資料失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
    }
  };

  // 初始載入
  useEffect(() => {
    if (alertService) {
      loadAlerts();
      loadStatistics();
    }
  }, [alertService]);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>資料存取層範例</Text>
      
      {/* 網路狀態 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>網路狀態</Text>
        <Text style={styles.status}>
          狀態: {isOnline ? '已連線' : '離線'}
        </Text>
        <Button title="檢查連線" onPress={checkConnection} />
      </View>

      {/* 統計資訊 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>統計資訊</Text>
        {statistics ? (
          <View>
            <Text>總警報數: {statistics.totalAlerts}</Text>
            <Text>待上傳警報: {statistics.unuploadedAlerts}</Text>
            <Text>警報檔案數: {statistics.totalAlertFiles}</Text>
          </View>
        ) : (
          <Text>載入中...</Text>
        )}
        <Button title="重新載入統計" onPress={loadStatistics} />
      </View>

      {/* 警報列表 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>警報列表 ({alerts.length} 筆)</Text>
        <Button title="重新載入" onPress={loadAlerts} />
        <Button title="上傳警報" onPress={uploadAlerts} />
        <Button title="清理過期資料" onPress={cleanupOldData} />
        
        {alerts.slice(0, 5).map((alert, index) => (
          <View key={alert.id || index} style={styles.alertItem}>
            <Text style={styles.alertText}>
              ID: {alert.id} | 類型: {alert.AlarmType} | 
              上傳狀態: {alert.upload ? '已上傳' : '待上傳'}
            </Text>
            <Text style={styles.alertTime}>
              時間: {alert.CheckTime}
            </Text>
          </View>
        ))}
        
        {alerts.length > 5 && (
          <Text style={styles.moreText}>... 還有 {alerts.length - 5} 筆記錄</Text>
        )}
      </View>

      {/* 服務狀態 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>服務狀態</Text>
        <Text>警報服務: {alertService ? '已初始化' : '未初始化'}</Text>
        <Text>網路服務: {networkService ? '已初始化' : '未初始化'}</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  status: {
    fontSize: 16,
    marginBottom: 8,
  },
  alertItem: {
    backgroundColor: '#f9f9f9',
    padding: 8,
    marginVertical: 4,
    borderRadius: 4,
    borderLeftWidth: 3,
    borderLeftColor: '#007AFF',
  },
  alertText: {
    fontSize: 14,
    fontWeight: '500',
  },
  alertTime: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  moreText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
  },
});

export default DatabaseServiceExample;
