{"expo": {"name": "Patroling", "slug": "mkpatrol2005", "owner": "kuoyitang", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"softwareKeyboardLayoutMode": "pan", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.NFC", "INTERNET", "ACCESS_NETWORK_STATE", "CAMERA", "ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION"], "package": "com.yitangkg.mklpatrol"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-sqlite", "react-native-nfc-manager", "expo-font", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "0408ea32-8bc2-4220-bb1e-a4a9851e58f7"}}}}