import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, TextInput, ScrollView, Image, Alert, Platform, Linking } from 'react-native';
import { useAppContext } from './context/AppContext';
import { Camera, CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import * as Location from 'expo-location';
// import { useAlertUpload } from './hooks/useAlertUpload';
import * as FileSystem from 'expo-file-system';
import { Ionicons } from '@expo/vector-icons';

type AlarmSendProps = {
  visible: boolean;
  onClose: () => void;
  alarmType: number;
};

const AlarmSend = ({ visible, onClose, alarmType }: AlarmSendProps) => {
  // const { UploadAlert } = useAlertUpload();
  const { db, globalData, uuidv4, formatDateTime, UploadAlert } = useAppContext();
  const [showCamera, setShowCamera] = useState(false);
  const [inProcess, setInProcess] = useState(false);
  const [photos, setPhotos] = useState<Array<{
    uri: string;
    latitude: number | null;
    longitude: number | null;
  }>>([]);
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number | null>(null);

  useEffect(() => {
    if (visible) {
    //   setPhotos([]);
      setDescription('');
      setInProcess(false);
    }
  }, [visible]);

  useEffect(() => {
    console.log('照片更新:', photos);
  }, [photos]);

  useEffect(() => {
    (async () => {
      try {
        // 檢查位置權限
        let { status: locationStatus } = await Location.getForegroundPermissionsAsync();
        
        if (locationStatus !== 'granted') {
          locationStatus = (await Location.requestForegroundPermissionsAsync()).status;
          if (locationStatus !== 'granted') {
            Alert.alert(
              globalData._words.warning || '警告',
              '需要位置權限才能繼續操作',
              [{ text: globalData._words.ok || '確定' }]
            );
            return;
          }
        }

        console.log('獲取位置資訊');
        let currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low,
          timeInterval: 5000,
          distanceInterval: 10
        });
        console.log('獲取位置資訊', currentLocation);
        setLocation(currentLocation);
      } catch (error) {
        console.error('權限或位置獲取失敗:', error);
        Alert.alert(
          globalData._words.warning || '警告',
          '無法獲取位置資訊',
          [{ text: globalData._words.ok || '確定' }]
        );
      }

      let { status: cameraStatus } = await Camera.getCameraPermissionsAsync();
        
      if (cameraStatus !== 'granted') {
        cameraStatus = (await Camera.requestCameraPermissionsAsync()).status;
        if (cameraStatus !== 'granted') {
          Alert.alert(
            globalData._words.warning || '警告',
            '需要相機權限才能繼續操作',
            [{ text: globalData._words.ok || '確定' }]
          );
          return;
        }
      }

    })();
  }, []);

  const clearPhotos = () => {
    setPhotos([]);
    setInProcess(false);
}

 /**
 * 拍照並獲取位置資訊
 * 1. 檢查相機權限，若未授權則請求權限
 * 2. 拍照後先保存照片URI，位置資訊設為null
 * 3. 獲取位置權限，若授權則嘗試獲取位置
 * 4. 更新最後一張照片的位置資訊
 * 5. 處理過程中任何錯誤都會顯示警告訊息
 */
const takePicture = async () => {
    if (cameraRef.current) {
      setInProcess(true); // 標記處理中狀態
      try {
        // 檢查相機權限
        let { status: cameraStatus } = await Camera.getCameraPermissionsAsync();
        
        if (cameraStatus !== 'granted') {
          cameraStatus = (await Camera.requestCameraPermissionsAsync()).status;
          if (cameraStatus !== 'granted') {
            Alert.alert(
              globalData._words.warning || '警告',
              '需要相機權限才能繼續操作',
              [{ text: globalData._words.ok || '確定' }]
            );
            setInProcess(false);
            return;
          }
        }

        // 原有的拍照邏輯
        const photo = await cameraRef.current.takePictureAsync({ quality: 0.5 });
        
        // 先將照片保存，但位置資訊為 null
        const newPhotos = [...photos, {
          uri: photo.uri,
          latitude: null,
          longitude: null
        }];
        setPhotos(newPhotos);
        
        // 關閉相機視圖
        setShowCamera(false);

        // 然後獲取位置權限和位置資訊
        const { status } = await Location.requestForegroundPermissionsAsync();

        if (status === 'granted') {
          // 先嘗試獲取最後已知位置
          // console.log('獲取位置資訊');
          let photoLocation;
          try {
            photoLocation = await Location.getLastKnownPositionAsync();
            if (!photoLocation) {
              // 如果沒有最後已知位置，則獲取當前位置，但使用較低精確度
              photoLocation = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Low,
                timeInterval: 5000,  // 5秒的時間間隔
                distanceInterval: 10 // 10公尺的距離間隔
              });
            }
          } catch (error) {
            console.log('位置獲取失敗，使用備選方案:', error);
            // 使用備選方案
            photoLocation = await Location.getCurrentPositionAsync({
              accuracy: Location.Accuracy.Low
            });
          }
          
          // console.log('獲取位置成功', photoLocation);
            
          // 更新最後一張照片的位置資訊
          const updatedPhotos = newPhotos.map((p, index) => {
            if (index === newPhotos.length - 1) {
              return {
                ...p,
                latitude: photoLocation.coords.latitude,
                longitude: photoLocation.coords.longitude
              };
            }
            return p;
          });
          
          setPhotos(updatedPhotos);
          setInProcess(false);
          // console.log('拍照和位置獲取成功', updatedPhotos);
        }
      } catch (error) {
        setInProcess(false);
        console.error('拍照或獲取位置時發生錯誤:', error);
        Alert.alert(
          globalData._words.warning || '警告',
          globalData._words.error || '拍照失敗',
          [{ text: globalData._words.ok || '確定' }]
        );
      }
    }
  };

  const cameraRef = React.useRef<CameraView>(null);

  const getAlarmTypeName = () => {
    switch (alarmType) {
      case 1:
        return globalData._words.alert1;
      case 2:
        return globalData._words.alert2;
      case 3:
        return globalData._words.alert3;
      case 4:
        return globalData._words.alert4;
      case 5:
        return globalData._words.alert5;
      case 6:
        return globalData._words.alert6;
      default:
        return '';
    }
  };

 /**
 * 處理警報發送
 * 1. 檢查資料庫是否可用
 * 2. 驗證必要輸入(如事件類型6需要描述)
 * 3. 顯示確認對話框
 * 4. 若確認則執行:
 *    - 生成唯一ID
 *    - 將警報存入本地資料庫
 *    - 儲存相關照片資訊
 *    - 觸發上傳程序
 *    - 根據設定嘗試發送簡訊或撥打電話
 * 5. 處理過程中任何錯誤都會顯示警告訊息
 */
const handleSend = async () => {
    if (!db) { // 檢查資料庫連接
      Alert.alert(
        globalData._words.warning || '警告',
        globalData._words.error || '資料庫未準備好',
        [{ text: globalData._words.ok || '確定' }]
      );
      return;
    }
    if (alarmType === 6) {
      if (!description || description.trim() === '') {
        Alert.alert(
          globalData._words.warning || '警告',
          globalData._words.inputAlmsg || '請輸入事件內容',
          [{ text: globalData._words.ok || '確定' }]
        );
        return;
      }
    }

    Alert.alert(
        globalData._words.warning || '警告',
        globalData._words.confirmAlert || '確定送出異常通報',
        [
          {
            text: globalData._words.confirm || '確定',
            onPress: async () => {
              try {
                // 在這裡執行發送警報的邏輯
                // console.log('發送警報', {
                //   alarmType,
                //   description,
                //   photos,
                //   location
                // });

                let guid = uuidv4();
                let alertId = ((new Date()).getTime() + guid).substr(0, 20);
                console.log('發送警報', {
                  alarmType,
                  description,
                  photos,
                  location,
                  alertId
                });
                await db.runAsync(
                  "insert into alerts (username,Guid,CountryId,FactoryId,PointId, UserId,CheckTime," +
                  "AlarmType,AlarmTime,Latitude, Longitude,AlarmDesc,PrecId,Photo,upload,trys) values (" +
                  "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?,?)",
                  [
                    globalData.userInfo.UserId,
                    alertId,
                    globalData.userInfo.CountryID,
                    globalData.userInfo.FactoryId,
                    '',
                    globalData.userInfo.UserId,
                    formatDateTime(new Date()),
                    alarmType,
                    formatDateTime(new Date()),
                    location ? location.coords.latitude : null,
                    location ? location.coords.longitude : null,
                    description,
                    '',
                    photos.length,
                    0,
                    0
                  ]
                );
                
                // 獲取最後插入的 ID
                const result = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
                const newId = result.id;
                console.log('新插入的記錄 ID:', newId);

                console.log('照片數:', photos.length);
                for (let i = 0; i < photos.length; i++) {
                  console.log('照片檔:', photos[i].uri);
                  db.runAsync(
                    "INSERT INTO alertfiles (alertId, path, PhotoId, time, Latitude, Longitude) VALUES " +
                    "(?, ?, ?, ?, ?, ?)",
                    [newId, photos[i].uri, uuidv4(), formatDateTime(new Date()), photos[i].latitude, photos[i].longitude]
                  );
                  const result = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
                  console.log('新照片的記錄 ID:', result.id);
        }

                // const resultFiles = await db.getFirstAsync('SELECT count(*) as cnt FROM alertfiles');
                // console.log('alertfiles count', resultFiles);

                // for await (const row of db.getEachAsync('SELECT * FROM alertfiles')) {
                //     console.log('alertfiles:', row.alertId, row.path);
                // }

                UploadAlert('alarmSend 323');

                let formattedNumber = ""
                if (globalData.org.Phone) {
                  formattedNumber = globalData.org.Phone.replace(/[^\d]/g, '');
                  const smsUrl = `sms:${formattedNumber}${Platform.OS === 'ios' ? '&' : '?'}body=${encodeURIComponent("")}`;
                  const canOpenSms = await Linking.canOpenURL(smsUrl);
                  
                  if (canOpenSms) {
                    await Linking.openURL(smsUrl);
                    await new Promise(resolve => setTimeout(resolve, 1500));
                  }        
                }

                if (globalData.org.Phone) {
                  formattedNumber = globalData.org.Phone.replace(/[^\d]/g, '');
                  const phoneUrl = `tel:${formattedNumber}`;
                  const canOpenPhone = await Linking.canOpenURL(phoneUrl);

                  if (canOpenPhone) {
                    await Linking.openURL(phoneUrl);
                    // await new Promise(resolve => setTimeout(resolve, 1500));
                  }

                  // const telUrl = `tel:${formattedNumber}`;
                  // const canOpenTel = await Linking.canOpenURL(telUrl);
                  
                  // if (canOpenTel) {
                  //   await Linking.openURL(telUrl);
                  // }          
                }

                onClose();
              } catch (error) {
                console.error('發送警報失敗:', error);
                Alert.alert(
                  globalData._words.warning || '警告',
                  globalData._words.error || '發送失敗',
                  [{ text: globalData._words.ok || '確定' }]
                );
              }
            }
          },
          {
            text: globalData._words.cancel || '取消',
            style: 'cancel'
          }
        ]
      );
  };

  const deletePhoto = async (index: number) => {
    try {
      const fileUri = photos[index].uri;
      await FileSystem.deleteAsync(fileUri);
      const newPhotos = photos.filter((_, i) => i !== index);
      setPhotos(newPhotos);
      setSelectedPhotoIndex(null);
    } catch (error) {
      console.error('刪除照片失敗:', error);
      Alert.alert(
        globalData._words.warning || '警告',
        '刪除照片失敗',
        [{ text: globalData._words.ok || '確定' }]
      );
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.alarmType}>
            {getAlarmTypeName()}
          </Text>

          {alarmType === 6 && (
            <TextInput
              style={styles.textInput}
              multiline={true}
              numberOfLines={4}
              value={description}
              onChangeText={setDescription}
              placeholder={globalData._words.inputAlmsg}
            />
          )}

          <View style={styles.photoSection}>
            {!showCamera ? (
              <>
                <ScrollView 
                  horizontal 
                  style={styles.photoScrollView}
                  contentContainerStyle={styles.photoContainer}
                >
                  {photos.map((photo, index) => (
                    <TouchableOpacity 
                      key={index}
                      onPress={() => setSelectedPhotoIndex(index)}
                    >
                      <Image 
                        source={{ uri: photo.uri }} 
                        style={styles.photoThumbnail}
                      />
                    </TouchableOpacity>
                  ))}
                </ScrollView>
                <View style={styles.cameraViewControls}>
                    <TouchableOpacity 
                        style={[styles.touchButton, styles.cameraButton]}
                        onPress={() => setShowCamera(true)}
                    >
                        <Ionicons name="camera" size={24} color="white" />
                    </TouchableOpacity>

                    <TouchableOpacity 
                        style={[styles.touchButton, styles.cameraButton]}
                        onPress={() => clearPhotos()}
                    >
                    <Ionicons name="trash" size={24} color="white" />
                    </TouchableOpacity>
                </View>
              </>

            ) : (
              <View style={styles.cameraContainer}>
                <CameraView
                  ref={cameraRef}
                  style={styles.camera}
                  facing="back"
                >
                  <View style={styles.cameraControls}>
                    <TouchableOpacity 
                      style={[styles.touchButton, styles.captureButton, inProcess && styles.sendButtonDisabled]}
                      disabled={inProcess}
                      onPress={takePicture}
                    >
                        <Ionicons name="checkmark" size={24} color="white" />
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={[styles.touchButton, styles.cancelCameraButton, inProcess && styles.sendButtonDisabled]}
                      disabled={inProcess}
                      onPress={() => {
                        setShowCamera(false);
                        setInProcess(false);
                      }}
                    >
                        <Ionicons name="close" size={24} color="white" />
                    </TouchableOpacity>
                  </View>
                </CameraView>

              </View>
            )}
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[
                styles.touchButton,
                styles.button, 
                styles.sendButton,
                inProcess && styles.sendButtonDisabled
              ]} 
              disabled={inProcess}
              onPress={handleSend}
            >
              <Text style={styles.buttonText}>{globalData._words.confirmAlertBtn}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.touchButton,
                styles.button, 
                styles.sendButton,
                inProcess && styles.sendButtonDisabled
              ]} 
              disabled={inProcess}
              onPress={onClose}
            >
              <Text style={styles.buttonText}>{globalData._words.cancel}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <Modal
        visible={selectedPhotoIndex !== null}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setSelectedPhotoIndex(null)}
      >
        <View style={styles.previewModalContainer}>
          <View style={styles.previewModalContent}>
            {selectedPhotoIndex !== null && (
              <>
                <Image
                  source={{ uri: photos[selectedPhotoIndex].uri }}
                  style={styles.previewImage}
                  resizeMode="contain"
                />
                <View style={styles.previewButtonContainer}>
                  <TouchableOpacity
                    style={[styles.previewButton, styles.deleteButton]}
                    onPress={() => deletePhoto(selectedPhotoIndex)}
                  >
                    <Text style={styles.buttonText}>{globalData._words.delete}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.previewButton, styles.backButton]}
                    onPress={() => setSelectedPhotoIndex(null)}
                  >
                    <Text style={styles.buttonText}>{globalData._words.cancel}</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '100%',
    height: '100%',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  alarmType: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    // position: 'absolute',
    // bottom: 80,
    // left: 0,
    // right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  button: {
    padding: 10,
    borderRadius: 5,
    width: '40%',
  },
  sendButton: {
    backgroundColor: '#4CAF50',
  },
  sendButtonDisabled: {
    backgroundColor: '#A5D6A7',  // 使用較淺的綠色
    opacity: 0.6,
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  photoSection: {
    marginBottom: 15,
    height: 380,
  },
  photoScrollView: {
    maxHeight: 300,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  photoContainer: {
    flexDirection: 'row',
    padding: 5,
  },
  photoThumbnail: {
    width: 150,
    height: 150,
    marginRight: 10,
    borderRadius: 5,
  },
  touchButton: {
    padding: 10,
    // paddingTop: 8,
    borderRadius: 5,
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cameraButton: {
    backgroundColor: '#2196F3',
    marginTop: 10,
  },
  cameraContainer: {
    flex: 1,
    borderRadius: 5,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  cameraViewControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    padding: 0,
    height: 60,
  },
  cameraControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    padding: 10,
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  captureButton: {
    backgroundColor: '#4CAF50',
    paddingLeft: 15,
    paddingRight: 15,
  },
  cancelCameraButton: {
    backgroundColor: '#F44336',
    paddingLeft: 15,
    paddingRight: 15,
  },
  previewModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewModalContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewImage: {
    width: '100%',
    height: '80%',
  },
  previewButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    padding: 20,
  },
  previewButton: {
    padding: 10,
    borderRadius: 5,
    width: '40%',
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  backButton: {
    backgroundColor: '#2196F3',
  },
});

export default AlarmSend;