import React, { useState, useEffect } from 'react';
import { View, Text, Button, ScrollView, StyleSheet, Alert } from 'react-native';
import { useAppContext } from '../context/AppContext';

// 警報上傳測試元件
export const UploadAlertExample: React.FC = () => {
  const { 
    UploadAlert, 
    alertQueue, 
    logs, 
    globalData, 
    alertService,
    isOnline 
  } = useAppContext();
  
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStats, setUploadStats] = useState<{
    total: number;
    unuploaded: number;
    uploaded: number;
  } | null>(null);

  // 載入統計資訊
  const loadStats = async () => {
    if (!alertService) return;
    
    try {
      const stats = await alertService.getStatistics();
      const allAlerts = await alertService.getAllAlerts();
      const uploadedCount = allAlerts.filter(alert => alert.upload === 1).length;
      
      setUploadStats({
        total: stats.totalAlerts,
        unuploaded: stats.unuploadedAlerts,
        uploaded: uploadedCount
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  // 手動觸發上傳
  const handleUpload = async () => {
    if (isUploading) return;
    
    setIsUploading(true);
    try {
      await UploadAlert('manual trigger');
      await loadStats(); // 重新載入統計
    } catch (error) {
      Alert.alert('上傳錯誤', error instanceof Error ? error.message : '未知錯誤');
    } finally {
      setIsUploading(false);
    }
  };

  // 模擬新增警報（用於測試）
  const addTestAlert = async () => {
    if (!alertService) return;
    
    try {
      // 這裡需要實際的資料庫服務來新增測試警報
      // 在實際應用中，警報會由其他功能新增
      Alert.alert('提示', '請使用實際的巡邏功能來新增警報');
    } catch (error) {
      Alert.alert('新增失敗', error instanceof Error ? error.message : '未知錯誤');
    }
  };

  // 檢查巡檢點狀態
  const checkPatrolPoints = () => {
    const prsWithCheckTime = globalData.org.prs.filter((prs: any) => prs.CheckTime);
    const prsUploaded = globalData.org.prs.filter((prs: any) => prs.upload);
    
    Alert.alert(
      '巡檢點狀態',
      `總巡檢點: ${globalData.org.prs.length}\n` +
      `已簽到: ${prsWithCheckTime.length}\n` +
      `已上傳: ${prsUploaded.length}`
    );
  };

  // 初始載入
  useEffect(() => {
    loadStats();
  }, [alertService]);

  // 監聽警報佇列變化
  useEffect(() => {
    loadStats();
  }, [alertQueue]);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>警報上傳測試</Text>
      
      {/* 網路狀態 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>系統狀態</Text>
        <Text style={styles.statusText}>
          網路狀態: {isOnline ? '🟢 已連線' : '🔴 離線'}
        </Text>
        <Text style={styles.statusText}>
          警報服務: {alertService ? '🟢 已初始化' : '🔴 未初始化'}
        </Text>
      </View>

      {/* 統計資訊 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>警報統計</Text>
        {uploadStats ? (
          <View>
            <Text style={styles.statText}>總警報數: {uploadStats.total}</Text>
            <Text style={styles.statText}>待上傳: {uploadStats.unuploaded}</Text>
            <Text style={styles.statText}>已上傳: {uploadStats.uploaded}</Text>
          </View>
        ) : (
          <Text>載入中...</Text>
        )}
        <Button title="重新載入統計" onPress={loadStats} />
      </View>

      {/* 巡檢點資訊 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>巡檢點資訊</Text>
        <Text style={styles.statText}>
          總巡檢點: {globalData.org.prs.length}
        </Text>
        <Text style={styles.statText}>
          已簽到: {globalData.org.prs.filter((prs: any) => prs.CheckTime).length}
        </Text>
        <Text style={styles.statText}>
          已上傳: {globalData.org.prs.filter((prs: any) => prs.upload).length}
        </Text>
        <Button title="檢查巡檢點狀態" onPress={checkPatrolPoints} />
      </View>

      {/* 操作按鈕 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>操作</Text>
        <View style={styles.buttonContainer}>
          <Button 
            title={isUploading ? "上傳中..." : "手動上傳警報"} 
            onPress={handleUpload}
            disabled={isUploading || !isOnline}
          />
        </View>
        <View style={styles.buttonContainer}>
          <Button 
            title="新增測試警報" 
            onPress={addTestAlert}
          />
        </View>
      </View>

      {/* 警報佇列 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>警報佇列 ({alertQueue.length} 筆)</Text>
        {alertQueue.slice(0, 3).map((alert, index) => (
          <View key={alert.id || index} style={styles.alertItem}>
            <Text style={styles.alertText}>
              ID: {alert.id} | 類型: {alert.AlarmType} | 
              狀態: {alert.upload ? '已上傳' : '待上傳'}
            </Text>
            <Text style={styles.alertTime}>
              巡檢點: {alert.PrecId} | 時間: {alert.CheckTime}
            </Text>
            {alert.AlarmType > 0 && (
              <Text style={styles.alertDesc}>
                異常描述: {alert.AlarmDesc}
              </Text>
            )}
          </View>
        ))}
        
        {alertQueue.length > 3 && (
          <Text style={styles.moreText}>... 還有 {alertQueue.length - 3} 筆記錄</Text>
        )}
      </View>

      {/* 最近日誌 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>最近日誌</Text>
        {logs.slice(-5).reverse().map((log, index) => (
          <View key={index} style={styles.logItem}>
            <Text style={styles.logTime}>
              {new Date(log.timestamp).toLocaleTimeString()}
            </Text>
            <Text style={styles.logMessage}>{log.message}</Text>
          </View>
        ))}
      </View>

      {/* 重要提示 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>重要說明</Text>
        <Text style={styles.infoText}>
          • 警報上傳成功後會自動更新巡檢點的簽到時間{'\n'}
          • 系統會自動處理重複上傳和網路重試{'\n'}
          • 圖片會在警報上傳成功後一併上傳{'\n'}
          • 上傳失敗的警報會自動重試
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  statusText: {
    fontSize: 16,
    marginBottom: 8,
  },
  statText: {
    fontSize: 16,
    marginBottom: 4,
  },
  buttonContainer: {
    marginVertical: 8,
  },
  alertItem: {
    backgroundColor: '#f9f9f9',
    padding: 12,
    marginVertical: 4,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  alertText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  alertTime: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  alertDesc: {
    fontSize: 12,
    color: '#e74c3c',
    fontStyle: 'italic',
  },
  logItem: {
    backgroundColor: '#f8f9fa',
    padding: 8,
    marginVertical: 2,
    borderRadius: 4,
    borderLeftWidth: 2,
    borderLeftColor: '#28a745',
  },
  logTime: {
    fontSize: 10,
    color: '#666',
    marginBottom: 2,
  },
  logMessage: {
    fontSize: 12,
    color: '#333',
  },
  moreText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#555',
  },
});

export default UploadAlertExample;
