# 資料存取層 (Data Access Layer)

這個資料存取層提供了一個清晰的架構來管理資料庫操作、網路請求和業務邏輯，將這些關注點從 React Context 中分離出來。

## 架構概覽

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Context │    │  Business Logic │    │  Data Access    │
│   (AppContext)   │───▶│  (AlertService) │───▶│ (DatabaseService│
│                 │    │                 │    │  NetworkService)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心服務

### 1. DatabaseService
負責所有資料庫操作，提供型別安全的 CRUD 介面。

**主要功能：**
- 警報記錄管理 (CRUD)
- 警報檔案管理
- 使用者資訊管理
- 資料庫統計
- 交易操作
- 資料清理

**使用範例：**
```typescript
import { createDatabaseService } from './services/DatabaseService';

// 初始化資料庫服務
const dbService = await createDatabaseService();

// 新增警報
await dbService.insertAlert({
  Guid: 'uuid-here',
  PrecId: 'point-1',
  username: 'user1',
  // ... 其他欄位
});

// 查詢未上傳的警報
const unuploadedAlerts = await dbService.getUnuploadedAlerts();

// 更新上傳狀態
await dbService.updateAlertUploadStatus(alertId, 1);
```

### 2. NetworkService
處理所有網路請求，包含重試機制和錯誤處理。

**主要功能：**
- 網路連線檢查
- 警報資料上傳
- 巡邏結束通知
- 圖片上傳
- 自動重試機制

**使用範例：**
```typescript
import { createNetworkService } from './services/NetworkService';

// 初始化網路服務
const networkService = createNetworkService({
  apiUrl: 'https://api.example.com',
  httpHeader: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token'
  },
  timeout: 10000,
  maxRetries: 2
});

// 檢查連線
const isOnline = await networkService.checkConnection();

// 上傳警報
const result = await networkService.uploadAlert(alertData);
```

### 3. AlertService
業務邏輯層，協調資料庫和網路操作。

**主要功能：**
- 警報批次上傳
- 圖片上傳管理
- 巡邏結束處理
- 資料清理
- 統計資訊

**使用範例：**
```typescript
import { AlertService } from './services/AlertService';

// 初始化警報服務
const alertService = new AlertService(databaseService, networkService);

// 批次上傳警報
const result = await alertService.uploadAlerts();
console.log(`處理 ${result.processed} 筆，成功 ${result.successful} 筆`);

// 結束巡邏
await alertService.endPatrol(patrolData, globalData);

// 取得統計資訊
const stats = await alertService.getStatistics();
```

## 在 React Context 中使用

### 1. 使用 useDatabaseService Hook
```typescript
import { useDatabaseService } from '../hooks/useDatabaseService';

export function AppProvider({ children }: { children: ReactNode }) {
  const { databaseService, isLoading, error } = useDatabaseService();
  
  // 當資料庫服務準備好時初始化其他服務
  useEffect(() => {
    if (databaseService) {
      // 初始化網路服務和警報服務
    }
  }, [databaseService]);
}
```

### 2. 在元件中使用服務
```typescript
import { useAppContext } from '../context/AppContext';

export function MyComponent() {
  const { alertService, networkService, isOnline } = useAppContext();
  
  const handleUpload = async () => {
    if (alertService) {
      const result = await alertService.uploadAlerts();
      // 處理結果
    }
  };
}
```

## 型別定義

### AlertRecord
```typescript
interface AlertRecord {
  id?: number;
  Guid: string;
  PrecId: string;
  username: string;
  CountryId: string;
  FactoryId: string;
  PointId: string;
  UserId: string;
  CheckTime: string;
  AlarmType: number;
  AlarmTime: string;
  Latitude: number;
  Longitude: number;
  AlarmDesc: string;
  Photo: number;
  trys: number;
  upload: number;
}
```

### AlertFile
```typescript
interface AlertFile {
  id?: number;
  alertId: number;
  path: string;
  PhotoId: string;
  time: string;
  Latitude: number;
  Longitude: number;
}
```

## 優勢

### 1. 關注點分離
- **DatabaseService**: 純資料存取，不包含業務邏輯
- **NetworkService**: 純網路操作，包含重試和錯誤處理
- **AlertService**: 業務邏輯，協調資料庫和網路操作
- **AppContext**: UI 狀態管理，不直接處理資料操作

### 2. 型別安全
- 所有資料庫操作都有明確的型別定義
- TypeScript 編譯時檢查，減少執行時錯誤

### 3. 可測試性
- 每個服務都可以獨立測試
- 可以輕鬆模擬依賴項目

### 4. 可維護性
- 清晰的架構，容易理解和修改
- 單一職責原則，每個類別只負責一件事

### 5. 可重用性
- 服務可以在不同的 Context 或元件中重用
- 不依賴特定的 UI 框架

## 遷移指南

### 從舊的 AppContext 遷移

1. **替換直接的資料庫操作**
   ```typescript
   // 舊方式
   const result = await db.getAllAsync('SELECT * FROM alerts');
   
   // 新方式
   const result = await databaseService.getAllAlerts();
   ```

2. **替換網路請求**
   ```typescript
   // 舊方式
   const response = await fetch(url, options);
   
   // 新方式
   const result = await networkService.uploadAlert(data);
   ```

3. **使用業務邏輯服務**
   ```typescript
   // 舊方式：在 Context 中處理複雜邏輯
   
   // 新方式：使用 AlertService
   const result = await alertService.uploadAlerts();
   ```

## 最佳實踐

1. **總是檢查服務是否已初始化**
   ```typescript
   if (!alertService) {
     console.log('Service not ready');
     return;
   }
   ```

2. **使用 try-catch 處理錯誤**
   ```typescript
   try {
     await alertService.uploadAlerts();
   } catch (error) {
     console.error('Upload failed:', error);
   }
   ```

3. **在適當的時候清理資源**
   ```typescript
   useEffect(() => {
     return () => {
       // 清理計時器、取消請求等
     };
   }, []);
   ```

4. **使用統計資訊監控系統狀態**
   ```typescript
   const stats = await alertService.getStatistics();
   console.log(`待上傳警報: ${stats.unuploadedAlerts}`);
   ```
