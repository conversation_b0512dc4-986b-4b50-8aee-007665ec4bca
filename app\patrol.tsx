import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useAppContext } from './context/AppContext';
import * as Location from 'expo-location';
import NfcManager, { NfcTech, NfcEvents } from 'react-native-nfc-manager';
import { router } from 'expo-router';

/**
 * PatrolScreen - 主巡邏畫面元件
 * 負責顯示巡邏路線、感應點狀態和NFC掃描功能
 */
const PatrolScreen = () => {
  // const { UploadAlert } = useAlertUpload();
  const navigation = useNavigation();
  const { db, globalData, formatDateTime, appMessage, setAppMessage, UploadAlert, endPatrol, addLog } = useAppContext();
  const [timeRange, setTimeRange] = useState('');
  const shiftPrs = useRef<any[]>([]);
  const myprsRef = useRef<any[]>([]);
  const [myprs, setMyprs] = useState<any[]>([]);
  const [nextPoint, setNextPoint] = useState<any>({Rfid: null});
  const [rangeTrigger, setRangeTrigger] = useState('');
  const [message, setMessage] = useState('');

  const [isScanning, setIsScanning] = useState(false);
  const [canScan, setCanScan] = useState(false);
  const isScanningRef = useRef(isScanning);
  
  let org = globalData.org;

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  const alarmImages = [
    require('../assets/images/fire.png'),
    require('../assets/images/flood.png'),
    require('../assets/images/intruder.png'),
    require('../assets/images/lobat.png'),
    require('../assets/images/electric.png')
  ];

  /**
 * 計算並格式化時間範圍字串
 * @param {Object} e - 包含StartTime和EndTime的物件
 * @returns {string} 格式化後的時間範圍字串
 */
const getRangeTime = (e: any) => {
    if (!!e.StartTime && !!e.EndTime) {
      return formatDateTime(new Date(e.StartTime)).substring(11, 16) + ' - ' + 
                      formatDateTime(new Date(e.EndTime)).substring(11, 16);
    }
    return '';
  }

  useEffect(() => {
    getShiftPrs();
    // 設定每小時更新的計時器
    const timer = setInterval(() => {
      getShiftPrs();
    }, 60000); // 每分鐘檢查一次

    if (__DEV__) {
      setAppMessage('debug');
      console.log('App is running in debug mode');
    } else {
      setAppMessage('production');
      console.log('App is running in production mode');
    }

    const initNfc = async () => {
      try {
        await NfcManager.start();

        addLog('NfcManager.setEventListener');

        // 設置監聽器來持續檢測NFC標籤
        NfcManager.setEventListener(NfcEvents.DiscoverTag, async (tag: any) => {
          if (isScanningRef.current) return;
          // let tagResult = '';
          if (!!tag && !!tag.id) {
            addLog('NfcManager.startScan');
            startScan(tag.id);
          } else {
            const tagData = await NfcManager.getTag();
            if (tagData && tagData.id) {

            }    
          }
        });

        // 開始監聽 NFC 標籤
        await NfcManager.registerTagEvent();

        // addLog('開始監聽 NFC 標籤');

      } catch (ex) {
        console.warn('NFC初始化錯誤:', ex);
        addLog('NFC初始化錯誤');
        // setTimeout(() => {
        //   startScan('');
        // }, 5000);
      }
    };

    initNfc();

    return () => {
      clearInterval(timer);

      const cleanUp = async () => {
        try {
          NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
          await NfcManager.unregisterTagEvent();
          // await NfcManager.cancelTechnologyRequest();
        } catch (error) {
          console.log('清理NFC時發生錯誤:', error);
        }
      };
      cleanUp();
    }
  }, []);

  const getShiftPrs = async() => {
    let now = new Date();
    let totalPrs = globalData.org.prs.filter((e: any) => (new Date(e.StartTime)) <= now && (new Date(e.EndTime)) > now);
    if (totalPrs.length > 0) {
      let oneprs = totalPrs[0];
      let newTimeRange = getRangeTime(oneprs);
      if (newTimeRange !== timeRange) {
        setRangeTrigger(newTimeRange);
      }
    }
  }

  useEffect(() => {
    let now = new Date();

    // console.log('ShiftPrs', shiftPrs.current);

    let totalPrs = org.prs.filter((e: any, index: any, array: any) => {
      const point = org.points.find((p: any) => p.PointId === e.PointId);
      e.PointName = point ? point.PointName : '';
      e.Rfid = point ? point.Rfid : '';
      e.loc = point && point.lat && point.lng;
      return ((new Date(e.StartTime)) <= now && (new Date(e.EndTime)) > now);
    });
    // console.log('totalPrs', totalPrs);
    shiftPrs.current = totalPrs;

    let myprsfiltered = totalPrs.filter((e: any, _index: any, array: any) => {
      return (e.UserId === globalData.userInfo.UserId);
    });
    // console.log('myprs UserId', myprsfiltered);
    
    if (myprsfiltered.length == 0) {
      // 取未感應的第一個路線
      myprsfiltered = totalPrs.filter((e: any, _index: any, array: any) => {
        return (!e.CheckTime && !e.UserId && e.CheckOrder == 1);
      });
      console.log('myprs CheckOrder == 1', myprsfiltered);
      if (myprsfiltered.length == 1) {
        // 只有一個路線
        const code = myprsfiltered[0].Code;
        myprsfiltered = totalPrs.filter((e: any, _index: any, array: any) => {
          return (e.Code == code);
        });
        console.log('myprs Code == ', code);
        setNextPoint(myprsfiltered[0]);
        // setMessage('nextpoint  ' + nextPoint.Rfid);
      } else {
        //console.log('超過一個路線', myprsfiltered.length);
        let nextprs = { PointName: 'Scan starting point', Rfid: '?' };
        setNextPoint(nextprs);
      }
      setCanScan(true);
    } else {
      if (myprsfiltered.every((e: any) => e.CheckTime)) {
        setNextPoint({ PointName: 'End Patrol', Rfid: null });
        setCanScan(false);
    } else {
        let nextprs = myprsfiltered.filter((e: any) => !e.CheckTime).sort((a: any, b: any) => a.CheckOrder - b.CheckOrder);
        // console.log('取未感應的', nextprs);
        if (nextprs.length > 0) {
          setNextPoint(nextprs[0]);
          // setMessage('nextpoint  ' + nextPoint.Rfid);
        }
        setCanScan(true);
      }
    }

    // console.log('myprsfiltered >>', myprsfiltered);
    setMyprs(myprsfiltered);
    // setCanScan(myprsfiltered.length > 0);
    
    if (totalPrs.length > 0) {
      setTimeRange(formatDateTime(new Date(totalPrs[0].StartTime)).substring(11, 16) + ' - ' + 
                  formatDateTime(new Date(totalPrs[0].EndTime)).substring(11, 16));
    }
  }, [rangeTrigger, globalData.org.prs]);

//   useEffect(() => {
//     navigation.setOptions({
//       headerStyle: styles.header,
//       headerRight: () => (
//         <MaterialIcons 
//           name={globalData.isOnline ? "wifi" : "wifi-off"} 
//           size={24} 
//           color={globalData.isOnline ? "#4CAF50" : "#F44336"}
//           style={{ marginRight: 16 }}
//         />
//       ),
//     });
//   }, [navigation, globalData.isOnline]);

  useEffect(() => {
    myprsRef.current = myprs;
  }, [myprs]);

  const debugScan = async () => {
    console.log('Debug Scan');
    let prs = myprsRef.current.find((e) => e.CheckTime == null);
    if (!!prs) {
      console.log('next points to scan', prs.Rfid);
      startScan(prs.Rfid);
    }
  }

  /**
 * 開始NFC掃描流程
 * @param {string} rfid - 掃描到的RFID標籤ID
 */
const startScan = async (rfid: string) => {
    isScanningRef.current = true;
    console.log('rfid', rfid);
    addLog('startScan-' + rfid);
    let prs = myprsRef.current.find((e) => e.Rfid == rfid);
    if (!prs) {
      console.log('RFID not found:', myprsRef.current);
      const found = org.points.find((e: any) => e.Rfid == rfid);
      if (!found) {
        console.log(`RFID not found: ${rfid}`, org.points);
      }
      console.log('About to show RFID not found alert', { found, rfid });
      alert(!found ? 'RFID not found' : 'wrong location:' + found.PointName);
      setMessage(!found ? 'RFID not found' : 'wrong location:' + found.PointName);
      setIsScanning(false);
      isScanningRef.current = false;
      return;
    }
    if (!!prs.CheckTime) {
      alert('Already checked');
      setMessage('Already checked');
      setIsScanning(false);
      isScanningRef.current = false;
      return;
    }
    let prevprs = myprsRef.current.filter((e) => e.Code == prs.Code && !e.CheckTime && e.CheckOrder < prs.CheckOrder);
    if (prevprs.length > 0) {
      alert(globalData._words.missOrd);
      setMessage(globalData._words.missOrd);
      setIsScanning(false);
      isScanningRef.current = false;
      return;
    }
    prs.CheckTime = new Date();
    if (prs.loc) {
      console.log('取得位置***************');
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        let scanLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low,
          timeInterval: 5000,  // 5秒的時間間隔
          distanceInterval: 10 // 10公尺的距離間隔
        });
        console.log('scanLocation', scanLocation);
        prs.latitude = scanLocation.coords.latitude;
        prs.longitude = scanLocation.coords.longitude;
      }
    }
    
    console.log('寫入新感應紀錄', prs)
    if (!!db) {
      await db.runAsync(
        "insert into alerts (username,Guid,CountryId,FactoryId,PointId, UserId,CheckTime," +
        "AlarmType,AlarmTime,Latitude, Longitude,AlarmDesc,PrecId,Photo,upload,trys) values (" +
        "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?,?)",
        [
          globalData.userInfo.UserId,
          "",
          globalData.userInfo.CountryID,
          globalData.userInfo.FactoryId,
          prs.PointId,
          globalData.userInfo.UserId,
          formatDateTime(prs.CheckTime),
          0,
          formatDateTime(new Date()),
          prs.latitude,
          prs.longitude,
          '',
          prs.PrecId,
          0,
          0,
          0
        ]
      );
    }

    // 傳入回調函數來處理上傳成功後的狀態更新
    await UploadAlert('patrol 302');

    prs.upload = false;
    // console.log('本次感應點', prs);
    let nextprs = myprsRef.current.find((e: any) => e.Code == prs.Code && !e.CheckTime && e.CheckOrder == prs.CheckOrder + 1);
    // console.log('下次感應點', nextprs);
    if (!prs.UserId) {
      // console.log('巡邏員未知', shiftPrs.current);
      prs.UserId = globalData.userInfo.UserId;

      let newMyPrs = shiftPrs.current.filter((e: any) => e.Code == prs.Code);
      newMyPrs.forEach((e: any) => {
        e.UserId = prs.UserId; //同路線每個點都指定userid
      });
      setMyprs(newMyPrs);
      // console.log('新感應點', newMyPrs);
      // console.log('舊感應點', myprs);
      nextprs = newMyPrs.find((e: any) => e.Code == prs.Code && !e.CheckTime && e.CheckOrder == prs.CheckOrder + 1);
    }

    if (!!nextprs) {
      setNextPoint(nextprs);
      // console.log('設定 setCanScan', true);
      setCanScan(true);
    } else {
      nextprs = { PointName: 'End Patrol', Rfid: null };
      setCanScan(false);

      console.log('結束巡邏', prs);
      setTimeout(() => { 
        endPatrol(prs); 
      }, 6000);
      // console.log('設定 setCanScan', false);
    }
    setNextPoint(nextprs);
    console.log('下次巡邏點 nextPoint', nextprs);
    setIsScanning(false);
    setMessage('OK');

    isScanningRef.current = false;
  }

  const refreshCheckTime = async () => {
    // 清除之前的計時器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }

  // 添加圖片輪播的 useEffect
  useEffect(() => {
    const imageInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % alarmImages.length);
    }, 5000); // 每10秒切換一次

    return () => clearInterval(imageInterval);
  }, []);

  /**
 * GridItem - 巡邏點列表項目元件
 * @param {Object} props - 元件屬性
 * @param {Object} props.item - 巡邏點資料
 * @param {number} props.count - 巡邏點總數
 */
const GridItem = ({ item, count }:{item: any, count: number}) => {
    return (
      <View style={styles.gridItem}>
        <View style={styles.fieldContainer}>
          <Text style={[count > 10 ? styles.pointText2 : styles.pointText, item.CheckTime ? styles.checked : (item.PrecId == nextPoint.PrecId ? styles.waiting : {}), { flex: 1 }]}>{item.CheckOrder + '. ' + item.PointName}</Text>
          <Text style={[count > 10 ? styles.pointText2 : styles.pointText, item.CheckTime ? styles.checked : {}, { flex: .3 }]}>{item.CheckTime ? formatDateTime(new Date(item.CheckTime)).substring(11, 16) : ''}</Text>

          {item.CheckTime && (
            <MaterialIcons 
              name={item.upload ? "check-circle" : "wifi-off"} 
              size={20} 
              color={item.upload ? "#4CAF50" : "#F44336"}
              style={{ width: 22 }}
            />
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* <View
        style={styles.textContainer}
      >
        <Text style={[styles.text, { flex: 1 }]}>{globalData.userInfo.UserName}</Text>
      </View> */}
      <View
        style={styles.textContainer}
      >
        <Text style={[styles.text]}>{globalData._words.checkTime}：</Text>
        <Text style={[styles.text, { color: 'blue', flex: 1 }]}>{timeRange}</Text>
      </View>
      {/* <View
        style={styles.textContainer}
      >
        <Text style={[styles.text]}>{globalData._words.nextPoint}：</Text>
        <Text style={[styles.text, { color: 'red', flex: 1 }]}>{nextPoint.PointName}</Text>
      </View> */}
      <View
        style={styles.textContainer}
      >
        <Text style={[styles.text, { flex: 1 }]}>{globalData._words.nextPoint}：</Text>
      </View>
      <View
        style={styles.textContainer}

      >
        <Text style={[styles.text, { color: 'red', flex: 1, textAlign: 'center' }]}>{nextPoint.PointName}</Text>
      </View>


      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.grid}>
          {myprs.map((item: any) => (
            <GridItem
              key={item.PrecId}
              item={item}
              count={myprs.length}
            />
          ))}
        </View>
      </ScrollView>
      <View style={styles.buttonContainer}>
        {/* <TouchableOpacity 
          style={[styles.button, (isScanning || !canScan) && styles.buttonDisabled]}
          onPress={() => startScan()}
          disabled={isScanning}
        >
          <Text style={styles.buttonText}>{globalData._words.arriveScan}</Text>
        </TouchableOpacity> */}
        
        {/* <TouchableOpacity 
          style={[styles.button, isScanning && styles.buttonDisabled]}
          onPress={() => navigation.navigate('Alarm' as never)}
          disabled={isScanning || !canScan}
        >
          <Text style={styles.buttonText}>{globalData._words.alert}</Text>
        </TouchableOpacity> */}

        <Text style={{fontSize: 12, textAlign: 'center', width: '100%'}} numberOfLines={4}>{appMessage}</Text>
        <Text style={{fontSize: 14, textAlign: 'center', width: '100%'}}>{message}</Text>
      </View>

      {__DEV__ && (
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button]}
          onPress={() => {
            debugScan();
          }}
        >
          <Text style={styles.buttonText}>{isScanning ? globalData._words.stopScan : globalData._words.startScan}</Text>
        </TouchableOpacity>
      </View>
      )}

      <TouchableOpacity 
        onPress={() => router.push('/alarm')}
        style={{position:'absolute', right: 12, bottom: 20}}
      >
        <Image 
          source={alarmImages[currentImageIndex]} 
          style={{width: 28, height: 28}} 
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 85,
    backgroundColor: 'ivory',
  },
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    padding: 0,
    backgroundColor: '#afd4f3',
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingLeft: 4,
    paddingBottom: 2,
  },
  text: {
  },
  buttonContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingTop: 15,
    paddingBottom: 15,
    minHeight: 100,
  },


  button: {
    backgroundColor: '#cdeb87',
    padding: 10,
    borderRadius: 5,
    minWidth: '60%',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
  },



  scrollViewContent: {
    flexGrow: 1,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'flex-start',
    flex: 1,
    minWidth: '100%',
    backgroundColor: '#f0f0f0',
    paddingLeft: 10,
    paddingTop: 5,
    minHeight: 300,
  },
  gridItem: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 3,
    borderBottomWidth: 1,
    borderBottomColor: '#aaa',
  },
  fieldContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  pointText: {
    fontSize: 14,
    marginHorizontal: 1,
    textAlign: 'left',
    flex: 1,
    color: 'blue'
  },
  pointText2: {
    fontSize: 12,
    marginHorizontal: 1,
    textAlign: 'left',
    flex: 1,
    color: 'blue'
  },
  checked: {
    color: 'green',
  },
  waiting: {
    color: 'red',
  },
  buttonDisabled: {
    backgroundColor: '#cccccc',
    opacity: 0.7,
  },
});

export default PatrolScreen;
