import React, { useState, useEffect } from 'react';
import { View, Text, Button, ScrollView, StyleSheet, Alert } from 'react-native';
import { useAppContext } from '../context/AppContext';

// 測試修復後的警報上傳功能
export const UploadAlertTestFixed: React.FC = () => {
  const { 
    UploadAlert, 
    alertQueue, 
    logs, 
    globalData, 
    alertService,
    isOnline 
  } = useAppContext();
  
  const [testResults, setTestResults] = useState<{
    uploadTest: string;
    signInTest: string;
    dataConsistency: string;
  }>({
    uploadTest: '未測試',
    signInTest: '未測試',
    dataConsistency: '未測試'
  });

  // 測試警報上傳功能
  const testUploadAlert = async () => {
    try {
      setTestResults(prev => ({ ...prev, uploadTest: '測試中...' }));
      
      // 檢查是否有待上傳的警報
      if (!alertService) {
        setTestResults(prev => ({ ...prev, uploadTest: '❌ AlertService 未初始化' }));
        return;
      }
      
      const stats = await alertService.getStatistics();
      if (stats.unuploadedAlerts === 0) {
        setTestResults(prev => ({ ...prev, uploadTest: '⚠️ 沒有待上傳的警報' }));
        return;
      }
      
      // 記錄上傳前的狀態
      const beforeUpload = {
        unuploadedCount: stats.unuploadedAlerts,
        prsWithCheckTime: globalData.org.prs.filter((prs: any) => prs.CheckTime).length
      };
      
      // 執行上傳
      await UploadAlert('test trigger');
      
      // 等待一段時間讓上傳完成
      setTimeout(async () => {
        const afterStats = await alertService.getStatistics();
        const afterPrsWithCheckTime = globalData.org.prs.filter((prs: any) => prs.CheckTime).length;
        
        const result = `✅ 上傳測試完成\n` +
          `上傳前待上傳: ${beforeUpload.unuploadedCount}\n` +
          `上傳後待上傳: ${afterStats.unuploadedAlerts}\n` +
          `簽到點增加: ${afterPrsWithCheckTime - beforeUpload.prsWithCheckTime}`;
        
        setTestResults(prev => ({ ...prev, uploadTest: result }));
      }, 3000);
      
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        uploadTest: `❌ 測試失敗: ${error instanceof Error ? error.message : '未知錯誤'}` 
      }));
    }
  };

  // 測試簽到時間更新功能
  const testSignInUpdate = async () => {
    try {
      setTestResults(prev => ({ ...prev, signInTest: '測試中...' }));
      
      // 檢查是否有正常簽到的警報（AlarmType = 0）
      const normalAlerts = alertQueue.filter(alert => 
        alert.AlarmType === 0 && alert.upload === 0
      );
      
      if (normalAlerts.length === 0) {
        setTestResults(prev => ({ 
          ...prev, 
          signInTest: '⚠️ 沒有正常簽到警報可測試' 
        }));
        return;
      }
      
      const testAlert = normalAlerts[0];
      const correspondingPrs = globalData.org.prs.find((prs: any) => 
        prs.PrecId === testAlert.PrecId
      );
      
      if (!correspondingPrs) {
        setTestResults(prev => ({ 
          ...prev, 
          signInTest: '⚠️ 找不到對應的巡檢點' 
        }));
        return;
      }
      
      const beforeCheckTime = correspondingPrs.CheckTime;
      const beforeUpload = correspondingPrs.upload;
      
      // 執行上傳
      await UploadAlert('sign-in test');
      
      // 檢查結果
      setTimeout(() => {
        const afterPrs = globalData.org.prs.find((prs: any) => 
          prs.PrecId === testAlert.PrecId
        );
        
        if (afterPrs) {
          const result = `✅ 簽到測試完成\n` +
            `巡檢點: ${testAlert.PrecId}\n` +
            `簽到前: ${beforeCheckTime ? '已簽到' : '未簽到'}\n` +
            `簽到後: ${afterPrs.CheckTime ? '已簽到' : '未簽到'}\n` +
            `上傳前: ${beforeUpload ? '已上傳' : '未上傳'}\n` +
            `上傳後: ${afterPrs.upload ? '已上傳' : '未上傳'}`;
          
          setTestResults(prev => ({ ...prev, signInTest: result }));
        } else {
          setTestResults(prev => ({ 
            ...prev, 
            signInTest: '❌ 測試後找不到巡檢點' 
          }));
        }
      }, 2000);
      
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        signInTest: `❌ 測試失敗: ${error instanceof Error ? error.message : '未知錯誤'}` 
      }));
    }
  };

  // 測試資料一致性
  const testDataConsistency = async () => {
    try {
      setTestResults(prev => ({ ...prev, dataConsistency: '檢查中...' }));
      
      if (!alertService) {
        setTestResults(prev => ({ ...prev, dataConsistency: '❌ AlertService 未初始化' }));
        return;
      }
      
      // 檢查警報佇列與資料庫的一致性
      const dbAlerts = await alertService.getAllAlerts();
      const queueAlerts = alertQueue;
      
      // 檢查巡檢點資料的完整性
      const prsWithCheckTime = globalData.org.prs.filter((prs: any) => prs.CheckTime);
      const prsWithUpload = globalData.org.prs.filter((prs: any) => prs.upload);
      
      // 檢查已上傳警報對應的巡檢點是否正確標記
      const uploadedAlerts = dbAlerts.filter(alert => alert.upload === 1);
      let consistentCount = 0;
      
      for (const alert of uploadedAlerts) {
        if (alert.AlarmType === 0) {
          const prs = globalData.org.prs.find((p: any) => p.PrecId === alert.PrecId);
          if (prs && prs.CheckTime && prs.upload) {
            consistentCount++;
          }
        }
      }
      
      const result = `✅ 資料一致性檢查完成\n` +
        `資料庫警報數: ${dbAlerts.length}\n` +
        `佇列警報數: ${queueAlerts.length}\n` +
        `已簽到巡檢點: ${prsWithCheckTime.length}\n` +
        `已上傳巡檢點: ${prsWithUpload.length}\n` +
        `資料一致的已上傳正常警報: ${consistentCount}/${uploadedAlerts.filter(a => a.AlarmType === 0).length}`;
      
      setTestResults(prev => ({ ...prev, dataConsistency: result }));
      
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        dataConsistency: `❌ 檢查失敗: ${error instanceof Error ? error.message : '未知錯誤'}` 
      }));
    }
  };

  // 重置測試結果
  const resetTests = () => {
    setTestResults({
      uploadTest: '未測試',
      signInTest: '未測試',
      dataConsistency: '未測試'
    });
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>警報上傳功能測試（修復版）</Text>
      
      {/* 系統狀態 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>系統狀態</Text>
        <Text style={styles.statusText}>
          網路: {isOnline ? '🟢 已連線' : '🔴 離線'}
        </Text>
        <Text style={styles.statusText}>
          警報服務: {alertService ? '🟢 已初始化' : '🔴 未初始化'}
        </Text>
        <Text style={styles.statusText}>
          待上傳警報: {alertQueue.filter(a => a.upload === 0).length} 筆
        </Text>
        <Text style={styles.statusText}>
          已簽到巡檢點: {globalData.org.prs.filter((p: any) => p.CheckTime).length} 個
        </Text>
      </View>

      {/* 測試按鈕 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>測試功能</Text>
        <View style={styles.buttonContainer}>
          <Button 
            title="測試警報上傳" 
            onPress={testUploadAlert}
            disabled={!isOnline || !alertService}
          />
        </View>
        <View style={styles.buttonContainer}>
          <Button 
            title="測試簽到時間更新" 
            onPress={testSignInUpdate}
            disabled={!isOnline || !alertService}
          />
        </View>
        <View style={styles.buttonContainer}>
          <Button 
            title="檢查資料一致性" 
            onPress={testDataConsistency}
            disabled={!alertService}
          />
        </View>
        <View style={styles.buttonContainer}>
          <Button 
            title="重置測試結果" 
            onPress={resetTests}
          />
        </View>
      </View>

      {/* 測試結果 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>測試結果</Text>
        
        <View style={styles.testResult}>
          <Text style={styles.testTitle}>警報上傳測試</Text>
          <Text style={styles.testContent}>{testResults.uploadTest}</Text>
        </View>
        
        <View style={styles.testResult}>
          <Text style={styles.testTitle}>簽到時間更新測試</Text>
          <Text style={styles.testContent}>{testResults.signInTest}</Text>
        </View>
        
        <View style={styles.testResult}>
          <Text style={styles.testTitle}>資料一致性檢查</Text>
          <Text style={styles.testContent}>{testResults.dataConsistency}</Text>
        </View>
      </View>

      {/* 最近日誌 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>最近日誌</Text>
        {logs.slice(-3).reverse().map((log, index) => (
          <View key={index} style={styles.logItem}>
            <Text style={styles.logTime}>
              {new Date(log.timestamp).toLocaleTimeString()}
            </Text>
            <Text style={styles.logMessage}>{log.message}</Text>
          </View>
        ))}
      </View>

      {/* 修復說明 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>修復說明</Text>
        <Text style={styles.infoText}>
          ✅ 恢復了原來的 UploadAlert → uploadAlert_ 呼叫機制{'\n'}
          ✅ 保持了簽到時間更新的業務邏輯{'\n'}
          ✅ 使用新的服務層進行資料存取{'\n'}
          ✅ 維持了原有的重試和錯誤處理機制{'\n'}
          ✅ 確保了資料一致性和完整性
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  statusText: {
    fontSize: 16,
    marginBottom: 8,
  },
  buttonContainer: {
    marginVertical: 8,
  },
  testResult: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    marginVertical: 8,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  testTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  testContent: {
    fontSize: 14,
    lineHeight: 20,
    color: '#555',
  },
  logItem: {
    backgroundColor: '#f8f9fa',
    padding: 8,
    marginVertical: 2,
    borderRadius: 4,
    borderLeftWidth: 2,
    borderLeftColor: '#28a745',
  },
  logTime: {
    fontSize: 10,
    color: '#666',
    marginBottom: 2,
  },
  logMessage: {
    fontSize: 12,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#555',
  },
});

export default UploadAlertTestFixed;
