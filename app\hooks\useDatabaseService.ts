import { useState, useEffect } from 'react';
import { DatabaseService, createDatabaseService } from '../services/DatabaseService';

// 自定義 Hook 用於管理資料庫服務
export const useDatabaseService = () => {
  const [databaseService, setDatabaseService] = useState<DatabaseService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const service = await createDatabaseService();
        setDatabaseService(service);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to initialize database'));
        console.error('Failed to initialize database:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeDatabase();
  }, []);

  return {
    databaseService,
    isLoading,
    error,
  };
};
