import { useSQLiteContext } from 'expo-sqlite';
import { useAppContext } from '../context/AppContext';
import React, { useEffect } from 'react';
import * as FileSystem from "expo-file-system";

interface LogEntry {
    timestamp: number;
    message: string;
  }
  
export const useAlertUpload_nouse = () => {
//   console.log('statrt useAlertUpload ********************');
  const db = useSQLiteContext();
  const { globalData, formatDateTime, setAlertQueue, setLogs, settings, setSettings } = useAppContext();  

  const maxRetries = 3;
  let uploadTimeoutId: any;

  const UploadAlert = async () => {
    console.log('UploadAlert called, clearTimeout(uploadTimeoutId)', uploadTimeoutId);
    clearTimeout(uploadTimeoutId);
    uploadAlert_();
  }

  const uploadAlert_ = async () => {
    console.log('uploadAlert_');
    let retryCount = 0;
    let newApiUrl = settings.apiUrl;
    setSettings((prev: any) => {
        newApiUrl = prev.apiUrl;
        return prev;
    });

    // const alerts = await db.getFirstAsync<{
    //     id: number;
    //     upload: number;
    //     Guid: string;
    //     CheckTime: any;
    //     AlarmTime: any;
    //     PrecId: string;
    //     UserId: string;
    //     AlarmType: number;
    //     }>(
    //     'SELECT * FROM alerts order by id limit 1'
    // );
    const allAlerts = await db.getAllAsync<{
        id: number;
        upload: number;
        Guid: string;
        CheckTime: any;
        AlarmTime: any;
        PrecId: string;
        UserId: string;
        AlarmType: number;
        }>(
        'SELECT * FROM alerts order by id'
    );

    setAlertQueue(allAlerts);
    if (allAlerts.length == 0) {
        console.log('uploadAlert_ alerts is null');
        return;
    }
    
    const alerts = allAlerts[0];
    console.log('uploadAlert_ alerts', alerts);
    // console.log('typeof(alerts.CheckTime)', typeof(alerts.CheckTime));
    // console.log('typeof(alerts.AlarmTime)', typeof(alerts.AlarmTime));

    const alertData = {
        ...alerts,
        CheckTime: formatDateTime(alerts.CheckTime),
        AlarmTime: formatDateTime(alerts.AlarmTime),
    };

    const tryUpload = async () => {
        while (retryCount < maxRetries) {
            try {
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('請求超時')), 10000);
                });

                console.log('tryUpload', newApiUrl);
                setLogs((prevLogs: LogEntry[]) => [
                    ...prevLogs,
                    {
                      timestamp: Date.now(),
                      message: 'tryUpload-' + newApiUrl
                    }
                  ]);

                const fetchPromise = fetch(newApiUrl + "/UploadAlert", {
                    method: 'POST',
                    headers: settings.httpHeader,
                    body: JSON.stringify(alertData),
                });


                const response1 = await Promise.race([fetchPromise, timeoutPromise]) as Response;
                // console.log('response1', response1);
                if (!response1.ok) {
                    console.log('!response1.ok', response1);
                    throw new Error('upload failed');
                }
                return await response1.json();
            } catch (error) {
                retryCount++;
                if (retryCount === maxRetries) {
                    throw error;
                }
                console.log(`upload失敗，第 ${retryCount} 次重試`);
                setLogs((prevLogs: LogEntry[]) => [
                    ...prevLogs,
                    {
                      timestamp: Date.now(),
                      message: `upload失敗，第 ${retryCount} 次重試`
                    }
                  ]);
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }
    };

    let retObj = { 
        AlarmType: 0, 
        AlarmTime: '', 
        Latitude: 0, 
        Longitude: 0, 
        PrecId: null, 
        Photo: 0, 
        upload: 0, 
        trys: 0, 
        success: false, 
        message: ''
    };

    try {
        if (alerts.upload == 0) {
            console.log('alerts.upload == 0');
            retObj = await tryUpload();
            console.log('retObj', retObj);
            if (retObj.success !== true) {
                if (retObj.message == "delete") {
                    console.log('message == delete, 取消上傳');
                } else {
                    console.warn('Upload success error 20秒重試');
                    uploadTimeoutId = setTimeout(uploadAlert_, 20000);
                    return;
                }
            }
            db.runAsync('UPDATE alerts SET upload = 1 WHERE id = ?', [alerts.id]);
        }

        if (alertData.AlarmType > 0) {
            const allRows = await db.getAllAsync<{
                alertId: number;
                PhotoId: string;
                path: string;
                Latitude: number;
                Longitude: number;
                time: string;
            }>('SELECT * FROM alertfiles where alertId = ?', [alerts.id]);
            
            if (allRows.length > 0) {
                let formData = new FormData();
                const mappedRows = allRows.map(row => {
                    formData.append('files', {
                        uri: row.path,
                        name: `${row.PhotoId}.jpg`,
                        type: 'image/jpeg',
                    } as any);
                    return {
                        AlertId: alerts.Guid,
                        CountryId: globalData.userInfo.CountryID,
                        UserId: globalData.userInfo.UserId,
                        PhotoId: row.PhotoId,
                        TakeTime: row.time,
                        Latitude: row.Latitude,
                        Longitude: row.Longitude
                    };
                });
                formData.append("datas", JSON.stringify(mappedRows));
                console.log('Mapped alert files:', mappedRows);

                const response = await fetch(newApiUrl + "/UploadAlertImages", {
                    method: 'POST',
                    body: formData,
                    headers: {
                    'Content-Type': 'multipart/form-data',
                    },
                });
            
                if (!response.ok) {
                    console.error('Upload formData error, 10秒重試');
                    uploadTimeoutId = setTimeout(uploadAlert_, 10000);
                    return;
                }
        
                const responseData = await response.json();
                console.log(responseData);
                console.log('Images uploaded successfully!');
                // Delete the files after upload
                for (const row of allRows) {
                    await FileSystem.deleteAsync(row.path);
                    console.log('delete', row.path);
                }
            }
        } else if (retObj.success == true) {
            setLogs((prevLogs: LogEntry[]) => [
                ...prevLogs,
                {
                  timestamp: Date.now(),
                  message: 'retObj.success'
                }
              ]);
            console.log('retObj.success', retObj.success);
            console.log('alertData.PrecId', alertData.PrecId);
            console.log('retObj.PrecId', retObj.PrecId);
            if (retObj.success && alertData.PrecId == retObj.PrecId) {
                console.log('網路斷線之後重新傳送，必須寫入簽到時間');
                let Prs = globalData.org.prs.find((e: any) => {
                    return (e.PrecId == retObj.PrecId);
                });
                console.log('globalData.org.Prs', Prs);
                if (Prs != null) {
                    if (!Prs.CheckTime) Prs.CheckTime = new Date(alertData.CheckTime);
                    Prs.UserId = alertData.UserId;
                    Prs.upload = true;
                    console.log(`網路斷線之後寫入簽到時間成功 `, Prs);
                }
            }            
        }


        const result = await db.runAsync('DELETE FROM alerts WHERE id = ?', [alerts.id]);
        const result2 = await db.runAsync('DELETE FROM alertfiles WHERE alertId = ?', [alerts.id]);
        console.log('result delete alerts', result, result2);
        console.log('uploadAlert_ 2秒後再上傳');
        uploadTimeoutId = setTimeout(uploadAlert_, 2000);

    } catch (error) {
        console.warn('Upload alert error 30秒重試:', error);
        setLogs((prevLogs: LogEntry[]) => [
            ...prevLogs,
            {
              timestamp: Date.now(),
              message: 'Upload alert error 30秒重試'
            }
          ]);
        uploadTimeoutId = setTimeout(uploadAlert_, 30000);
    }
  };

  useEffect(() => {
    console.log('useAlertUpload useEffect, globalData.isOnline change to', globalData.isOnline);    
    
    setLogs((prevLogs: LogEntry[]) => [
        ...prevLogs,
        {
          timestamp: Date.now(),
          message: '**useAlertUpload useEffect isOnline = ' + globalData.isOnline
        }
      ]);


    if (globalData.isOnline) {
        console.log('set uploadTimeoutId');
        setLogs((prevLogs: LogEntry[]) => [
            ...prevLogs,
            {
              timestamp: Date.now(),
              message: 'call uploadAlert_ in 5 sec'
            }
          ]);


        uploadTimeoutId = setTimeout(uploadAlert_, 5000);
    } else {
        clearTimeout(uploadTimeoutId);
    }

    return () => clearTimeout(uploadTimeoutId);
  }, [globalData.isOnline]); // 當網路狀態改變時重新設置計時器

  return { UploadAlert };
}

// 添加預設導出
export default useAlertUpload_nouse; 